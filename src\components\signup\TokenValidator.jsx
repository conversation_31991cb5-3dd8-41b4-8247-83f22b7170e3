import React from 'react';
import { useTokenValidation } from '../../hooks/useTokenValidation';
import { formatTimeRemaining } from '../../utils/tokenUtils';

const styles = {
  container: {
    maxWidth: '600px',
    margin: '0 auto',
    padding: '2rem',
    textAlign: 'center'
  },
  loading: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: '1rem',
    padding: '3rem'
  },
  spinner: {
    width: '40px',
    height: '40px',
    border: '4px solid #f3f3f3',
    borderTop: '4px solid #007bff',
    borderRadius: '50%',
    animation: 'spin 1s linear infinite'
  },
  error: {
    backgroundColor: '#fee',
    border: '1px solid #fcc',
    borderRadius: '8px',
    padding: '2rem',
    color: '#c33'
  },
  errorIcon: {
    fontSize: '3rem',
    marginBottom: '1rem'
  },
  valid: {
    backgroundColor: '#efe',
    border: '1px solid #cfc',
    borderRadius: '8px',
    padding: '1rem',
    color: '#363',
    marginBottom: '2rem'
  },
  validIcon: {
    fontSize: '2rem',
    marginBottom: '0.5rem'
  },
  tokenInfo: {
    backgroundColor: '#f8f9fa',
    border: '1px solid #dee2e6',
    borderRadius: '6px',
    padding: '1rem',
    marginBottom: '1rem',
    fontSize: '0.9rem',
    color: '#6c757d'
  }
};

function TokenValidator({ token, children }) {
  const { isLoading, isValid, tokenData, error } = useTokenValidation(token);

  // Add CSS animation for spinner
  React.useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    `;
    document.head.appendChild(style);
    return () => document.head.removeChild(style);
  }, []);

  if (isLoading) {
    return (
      <div style={styles.container}>
        <div style={styles.loading}>
          <div style={styles.spinner}></div>
          <h2>Validating your signup link...</h2>
          <p>Please wait while we verify your access token.</p>
        </div>
      </div>
    );
  }

  if (!isValid) {
    return (
      <div style={styles.container}>
        <div style={styles.error}>
          <div style={styles.errorIcon}>⚠️</div>
          <h2>Invalid or Expired Signup Link</h2>
          <p><strong>Error:</strong> {error}</p>
          
          <div style={{ marginTop: '2rem' }}>
            <h3>What can you do?</h3>
            <ul style={{ textAlign: 'left', maxWidth: '400px', margin: '0 auto' }}>
              <li>Check that you clicked the correct link from your email</li>
              <li>Ensure the link hasn't expired (links are valid for 24 hours)</li>
              <li>Contact our support team if you believe this is an error</li>
              <li>Request a new signup link if needed</li>
            </ul>
          </div>
          
          <div style={{ marginTop: '2rem' }}>
            <p><strong>Need help?</strong></p>
            <p>
              Email: <a href="mailto:<EMAIL>"><EMAIL></a><br/>
              Phone: 1-800-CONNECT
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div style={styles.container}>
      <div style={styles.valid}>
        <div style={styles.validIcon}>✅</div>
        <strong>Valid signup link confirmed!</strong>
      </div>
      
      {tokenData && (
        <div style={styles.tokenInfo}>
          <p><strong>Email:</strong> {tokenData.email}</p>
          <p><strong>Link expires:</strong> {formatTimeRemaining(tokenData.expiresAt)} remaining</p>
        </div>
      )}
      
      {children}
    </div>
  );
}

export default TokenValidator;
