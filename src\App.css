/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
  color: #333;
}

.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Landing page specific styles */
.landing-page main {
  flex: 1;
}

/* Terms page specific styles */
.terms-content {
  max-width: 800px;
  margin: 0 auto;
  padding: 3rem 2rem;
}

.terms-content h1 {
  color: #333;
  margin-bottom: 2rem;
  text-align: center;
}

.terms-text section {
  margin-bottom: 2rem;
}

.terms-text h2 {
  color: #007bff;
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.terms-text p {
  line-height: 1.6;
  margin-bottom: 1rem;
}

.terms-footer {
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 1px solid #dee2e6;
  text-align: center;
  color: #6c757d;
  font-size: 0.9rem;
}

/* Success page specific styles */
.success-content {
  max-width: 600px;
  margin: 0 auto;
  padding: 3rem 2rem;
}

.success-card {
  background: white;
  border-radius: 12px;
  padding: 3rem;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  text-align: center;
}

.success-icon {
  margin-bottom: 2rem;
}

.success-card h1 {
  color: #28a745;
  margin-bottom: 1rem;
}

.success-message {
  font-size: 1.1rem;
  color: #666;
  margin-bottom: 2rem;
}

.next-steps, .contact-info {
  text-align: left;
  margin: 2rem 0;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.next-steps h3, .contact-info h3 {
  color: #333;
  margin-bottom: 1rem;
}

.next-steps ul {
  padding-left: 1.2rem;
}

.next-steps li {
  margin-bottom: 0.5rem;
  color: #555;
}

.action-buttons {
  margin-top: 2rem;
}

.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover {
  background-color: #0056b3;
}

/* Responsive design */
@media (max-width: 768px) {
  .terms-content,
  .success-content {
    padding: 2rem 1rem;
  }

  .success-card {
    padding: 2rem;
  }
}
