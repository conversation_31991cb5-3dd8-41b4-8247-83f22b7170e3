import React from 'react';

const styles = {
  section: {
    padding: '6rem 0',
    backgroundColor: 'white'
  },
  container: {
    maxWidth: '1200px',
    margin: '0 auto',
    padding: '0 2rem'
  },
  header: {
    textAlign: 'center',
    marginBottom: '4rem'
  },
  title: {
    fontSize: '2.5rem',
    fontWeight: 'bold',
    marginBottom: '1rem',
    color: '#333'
  },
  subtitle: {
    fontSize: '1.2rem',
    color: '#666',
    maxWidth: '600px',
    margin: '0 auto'
  },
  testimonialsGrid: {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))',
    gap: '2rem',
    marginBottom: '4rem'
  },
  testimonialCard: {
    backgroundColor: '#f8f9fa',
    padding: '2rem',
    borderRadius: '12px',
    border: '1px solid #e9ecef',
    position: 'relative'
  },
  quote: {
    fontSize: '3rem',
    color: '#007bff',
    position: 'absolute',
    top: '1rem',
    left: '1.5rem',
    lineHeight: 1
  },
  testimonialText: {
    fontSize: '1.1rem',
    lineHeight: '1.6',
    color: '#333',
    marginBottom: '1.5rem',
    paddingTop: '1rem',
    fontStyle: 'italic'
  },
  author: {
    display: 'flex',
    alignItems: 'center',
    gap: '1rem'
  },
  authorAvatar: {
    width: '50px',
    height: '50px',
    borderRadius: '50%',
    backgroundColor: '#007bff',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: 'white',
    fontWeight: 'bold',
    fontSize: '1.2rem'
  },
  authorInfo: {
    flex: 1
  },
  authorName: {
    fontWeight: 'bold',
    color: '#333',
    marginBottom: '0.25rem'
  },
  authorTitle: {
    color: '#666',
    fontSize: '0.9rem'
  },
  statsSection: {
    backgroundColor: '#007bff',
    borderRadius: '12px',
    padding: '3rem 2rem',
    color: 'white',
    textAlign: 'center'
  },
  statsGrid: {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
    gap: '2rem'
  },
  statItem: {
    textAlign: 'center'
  },
  statNumber: {
    fontSize: '3rem',
    fontWeight: 'bold',
    marginBottom: '0.5rem',
    display: 'block'
  },
  statLabel: {
    fontSize: '1.1rem',
    opacity: 0.9
  }
};

function TestimonialsSection() {
  const testimonials = [
    {
      text: "Connect+ Apps transformed our restaurant operations completely. The integrated POS and customer management system helped us increase efficiency by 40% and customer satisfaction scores by 25%.",
      author: "Sarah Chen",
      title: "Owner, Golden Dragon Restaurant",
      initial: "S"
    },
    {
      text: "Managing our 5 retail locations was a nightmare before Connect+ Apps. Now we have real-time visibility across all stores, and our inventory management is seamless. It's been a game-changer.",
      author: "Michael Rodriguez",
      title: "Operations Manager, TechGear Plus",
      initial: "M"
    },
    {
      text: "The analytics and reporting features are incredible. We can now make data-driven decisions that have directly contributed to a 30% increase in revenue over the past year.",
      author: "Jennifer Thompson",
      title: "CEO, Wellness Spa Chain",
      initial: "J"
    },
    {
      text: "Customer support is outstanding, and the system is so intuitive that our staff was up and running in just a few hours. The ROI has been remarkable - we recovered our investment in just 3 months.",
      author: "David Park",
      title: "Franchise Owner, Quick Bites",
      initial: "D"
    },
    {
      text: "The security features give us peace of mind, especially with PCI compliance built-in. Our customers trust us with their data, and Connect+ Apps helps us maintain that trust.",
      author: "Lisa Wang",
      title: "IT Director, Metro Auto Services",
      initial: "L"
    },
    {
      text: "Integration with our existing accounting software was seamless. The API documentation is excellent, and their technical team provided amazing support throughout the implementation.",
      author: "Robert Johnson",
      title: "CFO, Professional Services Group",
      initial: "R"
    }
  ];

  const stats = [
    { number: "500+", label: "Businesses Served" },
    { number: "99.9%", label: "Uptime Guarantee" },
    { number: "24/7", label: "Customer Support" },
    { number: "35%", label: "Average Revenue Increase" }
  ];

  return (
    <section id="testimonials" style={styles.section}>
      <div style={styles.container}>
        <div style={styles.header}>
          <h2 style={styles.title}>What Our Customers Say</h2>
          <p style={styles.subtitle}>
            Join hundreds of successful businesses that have transformed their operations with Connect+ Apps.
          </p>
        </div>
        
        <div style={styles.testimonialsGrid}>
          {testimonials.map((testimonial, index) => (
            <div key={index} style={styles.testimonialCard}>
              <span style={styles.quote}>"</span>
              <p style={styles.testimonialText}>
                {testimonial.text}
              </p>
              <div style={styles.author}>
                <div style={styles.authorAvatar}>
                  {testimonial.initial}
                </div>
                <div style={styles.authorInfo}>
                  <div style={styles.authorName}>{testimonial.author}</div>
                  <div style={styles.authorTitle}>{testimonial.title}</div>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        <div style={styles.statsSection}>
          <h3 style={{fontSize: '2rem', marginBottom: '2rem', fontWeight: 'bold'}}>
            Trusted by Businesses Worldwide
          </h3>
          <div style={styles.statsGrid}>
            {stats.map((stat, index) => (
              <div key={index} style={styles.statItem}>
                <span style={styles.statNumber}>{stat.number}</span>
                <span style={styles.statLabel}>{stat.label}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}

export default TestimonialsSection;
