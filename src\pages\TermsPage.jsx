import React from 'react';
import Header from '../components/common/Header';
import Footer from '../components/common/Footer';

function TermsPage() {
  return (
    <div className="terms-page">
      <Header />
      <main className="terms-content">
        <div className="container">
          <h1>Terms and Conditions of Service</h1>
          <div className="terms-text">
            <section>
              <h2>1. Acceptance of Terms</h2>
              <p>
                By accessing and using Connect+ Apps services, you accept and agree to be bound by the terms and provision of this agreement.
              </p>
            </section>

            <section>
              <h2>2. Service Description</h2>
              <p>
                Connect+ Apps provides integrated business management solutions including point-of-sale systems, 
                customer management, and business analytics tools.
              </p>
            </section>

            <section>
              <h2>3. User Responsibilities</h2>
              <p>
                Users are responsible for maintaining the confidentiality of their account information and for all 
                activities that occur under their account.
              </p>
            </section>

            <section>
              <h2>4. Data Privacy and Security</h2>
              <p>
                We are committed to protecting your privacy and securing your data. All customer information is 
                encrypted and stored securely in compliance with industry standards.
              </p>
            </section>

            <section>
              <h2>5. Service Availability</h2>
              <p>
                While we strive for 99.9% uptime, we cannot guarantee uninterrupted service availability. 
                Scheduled maintenance will be communicated in advance.
              </p>
            </section>

            <section>
              <h2>6. Payment Terms</h2>
              <p>
                Service fees are billed monthly in advance. All fees are non-refundable except as required by law.
              </p>
            </section>

            <section>
              <h2>7. Limitation of Liability</h2>
              <p>
                Connect+ Apps shall not be liable for any indirect, incidental, special, consequential, or punitive damages.
              </p>
            </section>

            <section>
              <h2>8. Termination</h2>
              <p>
                Either party may terminate this agreement with 30 days written notice. Upon termination, 
                you will retain access to your data for 90 days for export purposes.
              </p>
            </section>

            <section>
              <h2>9. Changes to Terms</h2>
              <p>
                We reserve the right to modify these terms at any time. Users will be notified of significant 
                changes via email and continued use constitutes acceptance of modified terms.
              </p>
            </section>

            <section>
              <h2>10. Contact Information</h2>
              <p>
                For questions about these terms, please contact <NAME_EMAIL>
              </p>
            </section>
          </div>
          
          <div className="terms-footer">
            <p><strong>Last Updated:</strong> {new Date().toLocaleDateString()}</p>
            <p><strong>Version:</strong> 1.0</p>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}

export default TermsPage;
