{"version": 3, "sources": ["../../src/actions/registry.ts"], "sourcesContent": ["import type { Action, SourceIdPayload, TargetIdPayload } from '../interfaces.js'\n\nexport const ADD_SOURCE = 'dnd-core/ADD_SOURCE'\nexport const ADD_TARGET = 'dnd-core/ADD_TARGET'\nexport const REMOVE_SOURCE = 'dnd-core/REMOVE_SOURCE'\nexport const REMOVE_TARGET = 'dnd-core/REMOVE_TARGET'\n\nexport function addSource(sourceId: string): Action<SourceIdPayload> {\n\treturn {\n\t\ttype: ADD_SOURCE,\n\t\tpayload: {\n\t\t\tsourceId,\n\t\t},\n\t}\n}\n\nexport function addTarget(targetId: string): Action<TargetIdPayload> {\n\treturn {\n\t\ttype: ADD_TARGET,\n\t\tpayload: {\n\t\t\ttargetId,\n\t\t},\n\t}\n}\n\nexport function removeSource(sourceId: string): Action<SourceIdPayload> {\n\treturn {\n\t\ttype: REMOVE_SOURCE,\n\t\tpayload: {\n\t\t\tsourceId,\n\t\t},\n\t}\n}\n\nexport function removeTarget(targetId: string): Action<TargetIdPayload> {\n\treturn {\n\t\ttype: REMOVE_TARGET,\n\t\tpayload: {\n\t\t\ttargetId,\n\t\t},\n\t}\n}\n"], "names": ["ADD_SOURCE", "ADD_TARGET", "REMOVE_SOURCE", "REMOVE_TARGET", "addSource", "sourceId", "type", "payload", "addTarget", "targetId", "removeSource", "remove<PERSON>arget"], "mappings": "AAEA,OAAO,MAAMA,UAAU,GAAG,qBAAqB,CAAA;AAC/C,OAAO,MAAMC,UAAU,GAAG,qBAAqB,CAAA;AAC/C,OAAO,MAAMC,aAAa,GAAG,wBAAwB,CAAA;AACrD,OAAO,MAAMC,aAAa,GAAG,wBAAwB,CAAA;AAErD,OAAO,SAASC,SAAS,CAACC,QAAgB,EAA2B;IACpE,OAAO;QACNC,IAAI,EAAEN,UAAU;QAChBO,OAAO,EAAE;YACRF,QAAQ;SACR;KACD,CAAA;CACD;AAED,OAAO,SAASG,SAAS,CAACC,QAAgB,EAA2B;IACpE,OAAO;QACNH,IAAI,EAAEL,UAAU;QAChBM,OAAO,EAAE;YACRE,QAAQ;SACR;KACD,CAAA;CACD;AAED,OAAO,SAASC,YAAY,CAACL,QAAgB,EAA2B;IACvE,OAAO;QACNC,IAAI,EAAEJ,aAAa;QACnBK,OAAO,EAAE;YACRF,QAAQ;SACR;KACD,CAAA;CACD;AAED,OAAO,SAASM,YAAY,CAACF,QAAgB,EAA2B;IACvE,OAAO;QACNH,IAAI,EAAEH,aAAa;QACnBI,OAAO,EAAE;YACRE,QAAQ;SACR;KACD,CAAA;CACD"}