import React from 'react';
import { Link } from 'react-router-dom';

const styles = {
  header: {
    backgroundColor: '#fff',
    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
    position: 'sticky',
    top: 0,
    zIndex: 1000
  },
  nav: {
    maxWidth: '1200px',
    margin: '0 auto',
    padding: '1rem 2rem',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  logo: {
    fontSize: '1.5rem',
    fontWeight: 'bold',
    color: '#007bff',
    textDecoration: 'none'
  },
  navLinks: {
    display: 'flex',
    gap: '2rem',
    alignItems: 'center'
  },
  navLink: {
    color: '#333',
    textDecoration: 'none',
    fontWeight: '500',
    transition: 'color 0.3s ease'
  },
  contactButton: {
    backgroundColor: '#007bff',
    color: 'white',
    padding: '0.75rem 1.5rem',
    borderRadius: '4px',
    textDecoration: 'none',
    fontWeight: '500',
    transition: 'background-color 0.3s ease'
  }
};

function Header() {
  return (
    <header style={styles.header}>
      <nav style={styles.nav}>
        <Link to="/" style={styles.logo}>
          Connect+ Apps
        </Link>
        
        <div style={styles.navLinks}>
          <a 
            href="#features" 
            style={styles.navLink}
            onMouseOver={(e) => e.target.style.color = '#007bff'}
            onMouseOut={(e) => e.target.style.color = '#333'}
          >
            Features
          </a>
          <a 
            href="#testimonials" 
            style={styles.navLink}
            onMouseOver={(e) => e.target.style.color = '#007bff'}
            onMouseOut={(e) => e.target.style.color = '#333'}
          >
            Testimonials
          </a>
          <Link 
            to="/terms" 
            style={styles.navLink}
            onMouseOver={(e) => e.target.style.color = '#007bff'}
            onMouseOut={(e) => e.target.style.color = '#333'}
          >
            Terms
          </Link>
          <a 
            href="mailto:<EMAIL>" 
            style={styles.contactButton}
            onMouseOver={(e) => e.target.style.backgroundColor = '#0056b3'}
            onMouseOut={(e) => e.target.style.backgroundColor = '#007bff'}
          >
            Contact Us
          </a>
        </div>
      </nav>
    </header>
  );
}

export default Header;
