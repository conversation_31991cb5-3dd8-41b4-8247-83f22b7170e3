{"version": 3, "sources": ["../../../src/actions/dragDrop/types.ts"], "sourcesContent": ["export const INIT_COORDS = 'dnd-core/INIT_COORDS'\nexport const BEGIN_DRAG = 'dnd-core/BEGIN_DRAG'\nexport const PUBLISH_DRAG_SOURCE = 'dnd-core/PUBLISH_DRAG_SOURCE'\nexport const HOVER = 'dnd-core/HOVER'\nexport const DROP = 'dnd-core/DROP'\nexport const END_DRAG = 'dnd-core/END_DRAG'\n"], "names": ["INIT_COORDS", "BEGIN_DRAG", "PUBLISH_DRAG_SOURCE", "HOVER", "DROP", "END_DRAG"], "mappings": "AAAA,OAAO,MAAMA,WAAW,GAAG,sBAAsB,CAAA;AACjD,OAAO,MAAMC,UAAU,GAAG,qBAAqB,CAAA;AAC/C,OAAO,MAAMC,mBAAmB,GAAG,8BAA8B,CAAA;AACjE,OAAO,MAAMC,KAAK,GAAG,gBAAgB,CAAA;AACrC,OAAO,MAAMC,IAAI,GAAG,eAAe,CAAA;AACnC,OAAO,MAAMC,QAAQ,GAAG,mBAAmB,CAAA"}