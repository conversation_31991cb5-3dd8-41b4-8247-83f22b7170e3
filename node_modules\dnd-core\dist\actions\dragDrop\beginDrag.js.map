{"version": 3, "sources": ["../../../src/actions/dragDrop/beginDrag.ts"], "sourcesContent": ["import { invariant } from '@react-dnd/invariant'\n\nimport type {\n\tAction,\n\tBeginDragOptions,\n\tBeginDragPayload,\n\tDragDropManager,\n\tDragDropMonitor,\n\tHandlerRegistry,\n\tIdentifier,\n\tXYCoord,\n} from '../../interfaces.js'\nimport { isObject } from '../../utils/js_utils.js'\nimport { setClientOffset } from './local/setClientOffset.js'\nimport { BEGIN_DRAG, INIT_COORDS } from './types.js'\n\nconst ResetCoordinatesAction = {\n\ttype: INIT_COORDS,\n\tpayload: {\n\t\tclientOffset: null,\n\t\tsourceClientOffset: null,\n\t},\n}\n\nexport function createBeginDrag(manager: DragDropManager) {\n\treturn function beginDrag(\n\t\tsourceIds: Identifier[] = [],\n\t\toptions: BeginDragOptions = {\n\t\t\tpublishSource: true,\n\t\t},\n\t): Action<BeginDragPayload> | undefined {\n\t\tconst {\n\t\t\tpublishSource = true,\n\t\t\tclientOffset,\n\t\t\tgetSourceClientOffset,\n\t\t}: BeginDragOptions = options\n\t\tconst monitor = manager.getMonitor()\n\t\tconst registry = manager.getRegistry()\n\n\t\t// Initialize the coordinates using the client offset\n\t\tmanager.dispatch(setClientOffset(clientOffset))\n\n\t\tverifyInvariants(sourceIds, monitor, registry)\n\n\t\t// Get the draggable source\n\t\tconst sourceId = getDraggableSource(sourceIds, monitor)\n\t\tif (sourceId == null) {\n\t\t\tmanager.dispatch(ResetCoordinatesAction)\n\t\t\treturn\n\t\t}\n\n\t\t// Get the source client offset\n\t\tlet sourceClientOffset: XYCoord | null = null\n\t\tif (clientOffset) {\n\t\t\tif (!getSourceClientOffset) {\n\t\t\t\tthrow new Error('getSourceClientOffset must be defined')\n\t\t\t}\n\t\t\tverifyGetSourceClientOffsetIsFunction(getSourceClientOffset)\n\t\t\tsourceClientOffset = getSourceClientOffset(sourceId)\n\t\t}\n\n\t\t// Initialize the full coordinates\n\t\tmanager.dispatch(setClientOffset(clientOffset, sourceClientOffset))\n\n\t\tconst source = registry.getSource(sourceId)\n\t\tconst item = source.beginDrag(monitor, sourceId)\n\t\t// If source.beginDrag returns null, this is an indicator to cancel the drag\n\t\tif (item == null) {\n\t\t\treturn undefined\n\t\t}\n\t\tverifyItemIsObject(item)\n\t\tregistry.pinSource(sourceId)\n\n\t\tconst itemType = registry.getSourceType(sourceId)\n\t\treturn {\n\t\t\ttype: BEGIN_DRAG,\n\t\t\tpayload: {\n\t\t\t\titemType,\n\t\t\t\titem,\n\t\t\t\tsourceId,\n\t\t\t\tclientOffset: clientOffset || null,\n\t\t\t\tsourceClientOffset: sourceClientOffset || null,\n\t\t\t\tisSourcePublic: !!publishSource,\n\t\t\t},\n\t\t}\n\t}\n}\n\nfunction verifyInvariants(\n\tsourceIds: Identifier[],\n\tmonitor: DragDropMonitor,\n\tregistry: HandlerRegistry,\n) {\n\tinvariant(!monitor.isDragging(), 'Cannot call beginDrag while dragging.')\n\tsourceIds.forEach(function (sourceId) {\n\t\tinvariant(\n\t\t\tregistry.getSource(sourceId),\n\t\t\t'Expected sourceIds to be registered.',\n\t\t)\n\t})\n}\n\nfunction verifyGetSourceClientOffsetIsFunction(getSourceClientOffset: any) {\n\tinvariant(\n\t\ttypeof getSourceClientOffset === 'function',\n\t\t'When clientOffset is provided, getSourceClientOffset must be a function.',\n\t)\n}\n\nfunction verifyItemIsObject(item: any) {\n\tinvariant(isObject(item), 'Item must be an object.')\n}\n\nfunction getDraggableSource(sourceIds: Identifier[], monitor: DragDropMonitor) {\n\tlet sourceId = null\n\tfor (let i = sourceIds.length - 1; i >= 0; i--) {\n\t\tif (monitor.canDragSource(sourceIds[i])) {\n\t\t\tsourceId = sourceIds[i]\n\t\t\tbreak\n\t\t}\n\t}\n\treturn sourceId\n}\n"], "names": ["invariant", "isObject", "setClientOffset", "BEGIN_DRAG", "INIT_COORDS", "ResetCoordinatesAction", "type", "payload", "clientOffset", "sourceClientOffset", "createBeginDrag", "manager", "beginDrag", "sourceIds", "options", "publishSource", "getSourceClientOffset", "monitor", "getMonitor", "registry", "getRegistry", "dispatch", "verifyInvariants", "sourceId", "getDraggableSource", "Error", "verifyGetSourceClientOffsetIsFunction", "source", "getSource", "item", "undefined", "verifyItemIsObject", "pinSource", "itemType", "getSourceType", "isSourcePublic", "isDragging", "for<PERSON>ach", "i", "length", "canDragSource"], "mappings": "AAAA,SAASA,SAAS,QAAQ,sBAAsB,CAAA;AAYhD,SAASC,QAAQ,QAAQ,yBAAyB,CAAA;AAClD,SAASC,eAAe,QAAQ,4BAA4B,CAAA;AAC5D,SAASC,UAAU,EAAEC,WAAW,QAAQ,YAAY,CAAA;AAEpD,MAAMC,sBAAsB,GAAG;IAC9BC,IAAI,EAAEF,WAAW;IACjBG,OAAO,EAAE;QACRC,YAAY,EAAE,IAAI;QAClBC,kBAAkB,EAAE,IAAI;KACxB;CACD;AAED,OAAO,SAASC,eAAe,CAACC,OAAwB,EAAE;IACzD,OAAO,SAASC,SAAS,CACxBC,SAAuB,GAAG,EAAE,EAC5BC,OAAyB,GAAG;QAC3BC,aAAa,EAAE,IAAI;KACnB,EACsC;QACvC,MAAM,EACLA,aAAa,EAAG,IAAI,CAAA,EACpBP,YAAY,CAAA,EACZQ,qBAAqB,CAAA,IACrB,GAAqBF,OAAO;QAC7B,MAAMG,OAAO,GAAGN,OAAO,CAACO,UAAU,EAAE;QACpC,MAAMC,QAAQ,GAAGR,OAAO,CAACS,WAAW,EAAE;QAEtC,qDAAqD;QACrDT,OAAO,CAACU,QAAQ,CAACnB,eAAe,CAACM,YAAY,CAAC,CAAC;QAE/Cc,gBAAgB,CAACT,SAAS,EAAEI,OAAO,EAAEE,QAAQ,CAAC;QAE9C,2BAA2B;QAC3B,MAAMI,QAAQ,GAAGC,kBAAkB,CAACX,SAAS,EAAEI,OAAO,CAAC;QACvD,IAAIM,QAAQ,IAAI,IAAI,EAAE;YACrBZ,OAAO,CAACU,QAAQ,CAAChB,sBAAsB,CAAC;YACxC,OAAM;SACN;QAED,+BAA+B;QAC/B,IAAII,kBAAkB,GAAmB,IAAI;QAC7C,IAAID,YAAY,EAAE;YACjB,IAAI,CAACQ,qBAAqB,EAAE;gBAC3B,MAAM,IAAIS,KAAK,CAAC,uCAAuC,CAAC,CAAA;aACxD;YACDC,qCAAqC,CAACV,qBAAqB,CAAC;YAC5DP,kBAAkB,GAAGO,qBAAqB,CAACO,QAAQ,CAAC;SACpD;QAED,kCAAkC;QAClCZ,OAAO,CAACU,QAAQ,CAACnB,eAAe,CAACM,YAAY,EAAEC,kBAAkB,CAAC,CAAC;QAEnE,MAAMkB,MAAM,GAAGR,QAAQ,CAACS,SAAS,CAACL,QAAQ,CAAC;QAC3C,MAAMM,IAAI,GAAGF,MAAM,CAACf,SAAS,CAACK,OAAO,EAAEM,QAAQ,CAAC;QAChD,4EAA4E;QAC5E,IAAIM,IAAI,IAAI,IAAI,EAAE;YACjB,OAAOC,SAAS,CAAA;SAChB;QACDC,kBAAkB,CAACF,IAAI,CAAC;QACxBV,QAAQ,CAACa,SAAS,CAACT,QAAQ,CAAC;QAE5B,MAAMU,QAAQ,GAAGd,QAAQ,CAACe,aAAa,CAACX,QAAQ,CAAC;QACjD,OAAO;YACNjB,IAAI,EAAEH,UAAU;YAChBI,OAAO,EAAE;gBACR0B,QAAQ;gBACRJ,IAAI;gBACJN,QAAQ;gBACRf,YAAY,EAAEA,YAAY,IAAI,IAAI;gBAClCC,kBAAkB,EAAEA,kBAAkB,IAAI,IAAI;gBAC9C0B,cAAc,EAAE,CAAC,CAACpB,aAAa;aAC/B;SACD,CAAA;KACD,CAAA;CACD;AAED,SAASO,gBAAgB,CACxBT,SAAuB,EACvBI,OAAwB,EACxBE,QAAyB,EACxB;IACDnB,SAAS,CAAC,CAACiB,OAAO,CAACmB,UAAU,EAAE,EAAE,uCAAuC,CAAC;IACzEvB,SAAS,CAACwB,OAAO,CAAC,SAAUd,QAAQ,EAAE;QACrCvB,SAAS,CACRmB,QAAQ,CAACS,SAAS,CAACL,QAAQ,CAAC,EAC5B,sCAAsC,CACtC;KACD,CAAC;CACF;AAED,SAASG,qCAAqC,CAACV,qBAA0B,EAAE;IAC1EhB,SAAS,CACR,OAAOgB,qBAAqB,KAAK,UAAU,EAC3C,0EAA0E,CAC1E;CACD;AAED,SAASe,kBAAkB,CAACF,IAAS,EAAE;IACtC7B,SAAS,CAACC,QAAQ,CAAC4B,IAAI,CAAC,EAAE,yBAAyB,CAAC;CACpD;AAED,SAASL,kBAAkB,CAACX,SAAuB,EAAEI,OAAwB,EAAE;IAC9E,IAAIM,QAAQ,GAAG,IAAI;IACnB,IAAK,IAAIe,CAAC,GAAGzB,SAAS,CAAC0B,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,CAAE;QAC/C,IAAIrB,OAAO,CAACuB,aAAa,CAAC3B,SAAS,CAACyB,CAAC,CAAC,CAAC,EAAE;YACxCf,QAAQ,GAAGV,SAAS,CAACyB,CAAC,CAAC;YACvB,MAAK;SACL;KACD;IACD,OAAOf,QAAQ,CAAA;CACf"}