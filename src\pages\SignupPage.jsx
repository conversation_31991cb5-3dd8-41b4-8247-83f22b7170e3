import React from 'react';
import { useParams } from 'react-router-dom';
import TokenValidator from '../components/signup/TokenValidator';
import SignupForm from '../components/signup/SignupForm';
import Header from '../components/common/Header';
import Footer from '../components/common/Footer';

function SignupPage() {
  const { token } = useParams();

  return (
    <div className="signup-page">
      <Header />
      <main>
        <TokenValidator token={token}>
          <SignupForm />
        </TokenValidator>
      </main>
      <Footer />
    </div>
  );
}

export default SignupPage;
