import React from 'react';
import { Link } from 'react-router-dom';

const styles = {
  footer: {
    backgroundColor: '#333',
    color: '#fff',
    padding: '3rem 0 1rem'
  },
  container: {
    maxWidth: '1200px',
    margin: '0 auto',
    padding: '0 2rem'
  },
  footerContent: {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
    gap: '2rem',
    marginBottom: '2rem'
  },
  section: {
    display: 'flex',
    flexDirection: 'column'
  },
  sectionTitle: {
    fontSize: '1.2rem',
    fontWeight: 'bold',
    marginBottom: '1rem',
    color: '#007bff'
  },
  link: {
    color: '#ccc',
    textDecoration: 'none',
    marginBottom: '0.5rem',
    transition: 'color 0.3s ease'
  },
  contactInfo: {
    color: '#ccc',
    marginBottom: '0.5rem'
  },
  divider: {
    borderTop: '1px solid #555',
    margin: '2rem 0 1rem'
  },
  copyright: {
    textAlign: 'center',
    color: '#999',
    fontSize: '0.9rem'
  }
};

function Footer() {
  return (
    <footer style={styles.footer}>
      <div style={styles.container}>
        <div style={styles.footerContent}>
          <div style={styles.section}>
            <h3 style={styles.sectionTitle}>Connect+ Apps</h3>
            <p style={{color: '#ccc', lineHeight: '1.6'}}>
              Integrated business management solutions that help you streamline operations, 
              manage customers, and grow your business with confidence.
            </p>
          </div>
          
          <div style={styles.section}>
            <h3 style={styles.sectionTitle}>Quick Links</h3>
            <a 
              href="#features" 
              style={styles.link}
              onMouseOver={(e) => e.target.style.color = '#007bff'}
              onMouseOut={(e) => e.target.style.color = '#ccc'}
            >
              Features
            </a>
            <a 
              href="#testimonials" 
              style={styles.link}
              onMouseOver={(e) => e.target.style.color = '#007bff'}
              onMouseOut={(e) => e.target.style.color = '#ccc'}
            >
              Testimonials
            </a>
            <Link 
              to="/terms" 
              style={styles.link}
              onMouseOver={(e) => e.target.style.color = '#007bff'}
              onMouseOut={(e) => e.target.style.color = '#ccc'}
            >
              Terms of Service
            </Link>
          </div>
          
          <div style={styles.section}>
            <h3 style={styles.sectionTitle}>Contact Information</h3>
            <div style={styles.contactInfo}>
              <strong>Email:</strong> <EMAIL>
            </div>
            <div style={styles.contactInfo}>
              <strong>Support:</strong> <EMAIL>
            </div>
            <div style={styles.contactInfo}>
              <strong>Phone:</strong> 1-800-CONNECT
            </div>
            <div style={styles.contactInfo}>
              <strong>Hours:</strong> Mon-Fri 9AM-6PM EST
            </div>
          </div>
          
          <div style={styles.section}>
            <h3 style={styles.sectionTitle}>Business Solutions</h3>
            <div style={{color: '#ccc', marginBottom: '0.5rem'}}>• Point of Sale Systems</div>
            <div style={{color: '#ccc', marginBottom: '0.5rem'}}>• Customer Management</div>
            <div style={{color: '#ccc', marginBottom: '0.5rem'}}>• Inventory Tracking</div>
            <div style={{color: '#ccc', marginBottom: '0.5rem'}}>• Analytics & Reporting</div>
            <div style={{color: '#ccc', marginBottom: '0.5rem'}}>• Multi-location Support</div>
          </div>
        </div>
        
        <div style={styles.divider}></div>
        
        <div style={styles.copyright}>
          <p>© {new Date().getFullYear()} Connect+ Apps. All rights reserved.</p>
          <p style={{marginTop: '0.5rem', fontSize: '0.8rem'}}>
            Secure business management solutions for modern enterprises.
          </p>
        </div>
      </div>
    </footer>
  );
}

export default Footer;
