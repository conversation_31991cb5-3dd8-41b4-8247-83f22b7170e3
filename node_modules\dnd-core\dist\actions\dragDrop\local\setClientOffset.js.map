{"version": 3, "sources": ["../../../../src/actions/dragDrop/local/setClientOffset.ts"], "sourcesContent": ["import type { AnyAction } from 'redux'\n\nimport type { XYCoord } from '../../../interfaces.js'\nimport { INIT_COORDS } from '../types.js'\n\nexport function setClientOffset(\n\tclientOffset: XYCoord | null | undefined,\n\tsourceClientOffset?: XYCoord | null | undefined,\n): AnyAction {\n\treturn {\n\t\ttype: INIT_COORDS,\n\t\tpayload: {\n\t\t\tsourceClientOffset: sourceClientOffset || null,\n\t\t\tclientOffset: clientOffset || null,\n\t\t},\n\t}\n}\n"], "names": ["INIT_COORDS", "setClientOffset", "clientOffset", "sourceClientOffset", "type", "payload"], "mappings": "AAGA,SAASA,WAAW,QAAQ,aAAa,CAAA;AAEzC,OAAO,SAASC,eAAe,CAC9BC,YAAwC,EACxCC,kBAA+C,EACnC;IACZ,OAAO;QACNC,IAAI,EAAEJ,WAAW;QACjBK,OAAO,EAAE;YACRF,kBAAkB,EAAEA,kBAAkB,IAAI,IAAI;YAC9CD,YAAY,EAAEA,YAAY,IAAI,IAAI;SAClC;KACD,CAAA;CACD"}