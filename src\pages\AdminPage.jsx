import React, { useState } from 'react';
import { sendSignupInvitation, sendBulkInvitations } from '../services/emailService';
import Header from '../components/common/Header';
import Footer from '../components/common/Footer';

const styles = {
  container: {
    maxWidth: '800px',
    margin: '0 auto',
    padding: '2rem'
  },
  title: {
    textAlign: 'center',
    marginBottom: '2rem',
    color: '#333'
  },
  section: {
    backgroundColor: 'white',
    padding: '2rem',
    borderRadius: '8px',
    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
    marginBottom: '2rem'
  },
  sectionTitle: {
    fontSize: '1.3rem',
    fontWeight: 'bold',
    marginBottom: '1rem',
    color: '#007bff'
  },
  formGroup: {
    marginBottom: '1rem'
  },
  label: {
    display: 'block',
    marginBottom: '0.5rem',
    fontWeight: 'bold',
    color: '#333'
  },
  input: {
    width: '100%',
    padding: '0.75rem',
    border: '1px solid #ddd',
    borderRadius: '4px',
    fontSize: '1rem',
    boxSizing: 'border-box'
  },
  textarea: {
    width: '100%',
    padding: '0.75rem',
    border: '1px solid #ddd',
    borderRadius: '4px',
    fontSize: '1rem',
    minHeight: '100px',
    resize: 'vertical',
    boxSizing: 'border-box'
  },
  button: {
    backgroundColor: '#007bff',
    color: 'white',
    padding: '0.75rem 1.5rem',
    border: 'none',
    borderRadius: '4px',
    fontSize: '1rem',
    cursor: 'pointer',
    marginRight: '1rem'
  },
  buttonDisabled: {
    backgroundColor: '#6c757d',
    cursor: 'not-allowed'
  },
  success: {
    backgroundColor: '#d4edda',
    border: '1px solid #c3e6cb',
    color: '#155724',
    padding: '1rem',
    borderRadius: '4px',
    marginTop: '1rem'
  },
  error: {
    backgroundColor: '#f8d7da',
    border: '1px solid #f5c6cb',
    color: '#721c24',
    padding: '1rem',
    borderRadius: '4px',
    marginTop: '1rem'
  },
  results: {
    marginTop: '1rem',
    padding: '1rem',
    backgroundColor: '#f8f9fa',
    borderRadius: '4px',
    fontSize: '0.9rem'
  }
};

function AdminPage() {
  const [singleEmail, setSingleEmail] = useState('');
  const [bulkEmails, setBulkEmails] = useState('');
  const [senderName, setSenderName] = useState('Connect+ Apps Team');
  const [customMessage, setCustomMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [results, setResults] = useState(null);

  const handleSingleInvitation = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setResults(null);

    try {
      const result = await sendSignupInvitation(singleEmail, senderName, customMessage);
      
      if (result.success) {
        setResults({
          type: 'success',
          message: `Invitation sent successfully to ${singleEmail}`,
          details: `Token: ${result.token}`
        });
        setSingleEmail('');
      } else {
        setResults({
          type: 'error',
          message: `Failed to send invitation: ${result.error}`
        });
      }
    } catch (error) {
      setResults({
        type: 'error',
        message: 'An unexpected error occurred'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleBulkInvitations = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setResults(null);

    try {
      const emailList = bulkEmails
        .split('\n')
        .map(email => email.trim())
        .filter(email => email.length > 0);

      if (emailList.length === 0) {
        setResults({
          type: 'error',
          message: 'Please enter at least one email address'
        });
        setIsLoading(false);
        return;
      }

      const result = await sendBulkInvitations(emailList, senderName, customMessage);
      
      setResults({
        type: 'success',
        message: `Bulk invitation process completed`,
        details: `
          Total emails processed: ${result.total}
          Successful: ${result.successful.length}
          Failed: ${result.failed.length}
          Invalid emails: ${result.invalid.length}
          Duplicates: ${result.duplicates.length}
        `,
        bulkResults: result
      });
      
      setBulkEmails('');
    } catch (error) {
      setResults({
        type: 'error',
        message: 'An unexpected error occurred during bulk send'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div>
      <Header />
      <main style={{ backgroundColor: '#f8f9fa', minHeight: '80vh', padding: '2rem 0' }}>
        <div style={styles.container}>
          <h1 style={styles.title}>Connect+ Apps Admin Panel</h1>
          
          {/* Single Invitation Section */}
          <div style={styles.section}>
            <h2 style={styles.sectionTitle}>Send Single Invitation</h2>
            <form onSubmit={handleSingleInvitation}>
              <div style={styles.formGroup}>
                <label style={styles.label}>Email Address</label>
                <input
                  type="email"
                  value={singleEmail}
                  onChange={(e) => setSingleEmail(e.target.value)}
                  style={styles.input}
                  required
                  placeholder="<EMAIL>"
                />
              </div>
              
              <div style={styles.formGroup}>
                <label style={styles.label}>Sender Name</label>
                <input
                  type="text"
                  value={senderName}
                  onChange={(e) => setSenderName(e.target.value)}
                  style={styles.input}
                  placeholder="Connect+ Apps Team"
                />
              </div>
              
              <div style={styles.formGroup}>
                <label style={styles.label}>Custom Message (Optional)</label>
                <textarea
                  value={customMessage}
                  onChange={(e) => setCustomMessage(e.target.value)}
                  style={styles.textarea}
                  placeholder="Add a personal message to the invitation email..."
                />
              </div>
              
              <button
                type="submit"
                disabled={isLoading}
                style={{
                  ...styles.button,
                  ...(isLoading ? styles.buttonDisabled : {})
                }}
              >
                {isLoading ? 'Sending...' : 'Send Invitation'}
              </button>
            </form>
          </div>
          
          {/* Bulk Invitations Section */}
          <div style={styles.section}>
            <h2 style={styles.sectionTitle}>Send Bulk Invitations</h2>
            <form onSubmit={handleBulkInvitations}>
              <div style={styles.formGroup}>
                <label style={styles.label}>Email Addresses (one per line)</label>
                <textarea
                  value={bulkEmails}
                  onChange={(e) => setBulkEmails(e.target.value)}
                  style={{...styles.textarea, minHeight: '150px'}}
                  placeholder="<EMAIL>&#10;<EMAIL>&#10;<EMAIL>"
                />
              </div>
              
              <button
                type="submit"
                disabled={isLoading}
                style={{
                  ...styles.button,
                  ...(isLoading ? styles.buttonDisabled : {})
                }}
              >
                {isLoading ? 'Sending...' : 'Send Bulk Invitations'}
              </button>
            </form>
          </div>
          
          {/* Results Section */}
          {results && (
            <div style={results.type === 'success' ? styles.success : styles.error}>
              <strong>{results.message}</strong>
              {results.details && (
                <div style={styles.results}>
                  <pre>{results.details}</pre>
                </div>
              )}
              
              {results.bulkResults && (
                <div style={styles.results}>
                  <h4>Detailed Results:</h4>
                  
                  {results.bulkResults.successful.length > 0 && (
                    <div>
                      <strong>Successful ({results.bulkResults.successful.length}):</strong>
                      <ul>
                        {results.bulkResults.successful.map((item, index) => (
                          <li key={index}>{item.email} - Token: {item.token}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                  
                  {results.bulkResults.failed.length > 0 && (
                    <div>
                      <strong>Failed ({results.bulkResults.failed.length}):</strong>
                      <ul>
                        {results.bulkResults.failed.map((item, index) => (
                          <li key={index}>{item.email} - Error: {item.error}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                  
                  {results.bulkResults.invalid.length > 0 && (
                    <div>
                      <strong>Invalid Emails ({results.bulkResults.invalid.length}):</strong>
                      <ul>
                        {results.bulkResults.invalid.map((email, index) => (
                          <li key={index}>{email}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                  
                  {results.bulkResults.duplicates.length > 0 && (
                    <div>
                      <strong>Duplicate Emails ({results.bulkResults.duplicates.length}):</strong>
                      <ul>
                        {results.bulkResults.duplicates.map((email, index) => (
                          <li key={index}>{email}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
          
          {/* Instructions */}
          <div style={styles.section}>
            <h2 style={styles.sectionTitle}>Instructions</h2>
            <ul>
              <li>Use the single invitation form to send one invitation at a time</li>
              <li>Use the bulk invitation form to send multiple invitations by entering email addresses, one per line</li>
              <li>All invitation links are valid for 24 hours</li>
              <li>Custom messages will be included in the invitation email</li>
              <li>Invalid email addresses will be skipped and reported</li>
              <li>Duplicate email addresses will be removed automatically</li>
            </ul>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}

export default AdminPage;
