import React from 'react';

const styles = {
  section: {
    padding: '6rem 0',
    backgroundColor: '#f8f9fa'
  },
  container: {
    maxWidth: '1200px',
    margin: '0 auto',
    padding: '0 2rem'
  },
  header: {
    textAlign: 'center',
    marginBottom: '4rem'
  },
  title: {
    fontSize: '2.5rem',
    fontWeight: 'bold',
    marginBottom: '1rem',
    color: '#333'
  },
  subtitle: {
    fontSize: '1.2rem',
    color: '#666',
    maxWidth: '600px',
    margin: '0 auto'
  },
  featuresGrid: {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))',
    gap: '3rem'
  },
  featureCard: {
    backgroundColor: 'white',
    padding: '2.5rem',
    borderRadius: '12px',
    boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
    transition: 'transform 0.3s ease, box-shadow 0.3s ease'
  },
  featureIcon: {
    fontSize: '3rem',
    marginBottom: '1.5rem',
    display: 'block'
  },
  featureTitle: {
    fontSize: '1.5rem',
    fontWeight: 'bold',
    marginBottom: '1rem',
    color: '#333'
  },
  featureDescription: {
    color: '#666',
    lineHeight: '1.6',
    marginBottom: '1.5rem'
  },
  featureList: {
    listStyle: 'none',
    padding: 0,
    margin: 0
  },
  featureListItem: {
    color: '#555',
    marginBottom: '0.5rem',
    paddingLeft: '1.5rem',
    position: 'relative'
  },
  checkmark: {
    position: 'absolute',
    left: 0,
    color: '#28a745',
    fontWeight: 'bold'
  }
};

function FeaturesSection() {
  const features = [
    {
      icon: '💳',
      title: 'Advanced Point of Sale',
      description: 'Complete POS solution with inventory management, payment processing, and real-time synchronization across all locations.',
      items: [
        'Multi-payment method support',
        'Real-time inventory tracking',
        'Employee management & permissions',
        'Offline mode capability',
        'Custom receipt templates'
      ]
    },
    {
      icon: '👤',
      title: 'Customer Relationship Management',
      description: 'Build stronger customer relationships with comprehensive profiles, purchase history, and automated communication tools.',
      items: [
        'Detailed customer profiles',
        'Purchase history tracking',
        'Loyalty program management',
        'Automated email campaigns',
        'Customer feedback collection'
      ]
    },
    {
      icon: '📈',
      title: 'Business Intelligence & Analytics',
      description: 'Make informed decisions with powerful analytics, custom reports, and real-time business insights.',
      items: [
        'Real-time sales dashboards',
        'Custom report generation',
        'Trend analysis & forecasting',
        'Performance benchmarking',
        'Export capabilities'
      ]
    },
    {
      icon: '🏢',
      title: 'Multi-Location Management',
      description: 'Manage multiple business locations from a single platform with centralized control and location-specific insights.',
      items: [
        'Centralized management dashboard',
        'Location-specific reporting',
        'Inventory transfer between locations',
        'Staff scheduling across locations',
        'Unified customer database'
      ]
    },
    {
      icon: '🔐',
      title: 'Security & Compliance',
      description: 'Enterprise-grade security with PCI compliance, data encryption, and comprehensive audit trails.',
      items: [
        'PCI DSS compliance',
        'End-to-end encryption',
        'Role-based access control',
        'Audit trail logging',
        'Regular security updates'
      ]
    },
    {
      icon: '🔧',
      title: 'Integration & Customization',
      description: 'Seamlessly integrate with existing tools and customize the platform to match your business processes.',
      items: [
        'API access for integrations',
        'Custom workflow automation',
        'Third-party app connections',
        'Branded customer interfaces',
        'Flexible configuration options'
      ]
    }
  ];

  return (
    <section id="features" style={styles.section}>
      <div style={styles.container}>
        <div style={styles.header}>
          <h2 style={styles.title}>Powerful Features for Every Business</h2>
          <p style={styles.subtitle}>
            Discover how Connect+ Apps can transform your business operations with our comprehensive suite of integrated tools.
          </p>
        </div>
        
        <div style={styles.featuresGrid}>
          {features.map((feature, index) => (
            <div 
              key={index} 
              style={styles.featureCard}
              onMouseOver={(e) => {
                e.currentTarget.style.transform = 'translateY(-5px)';
                e.currentTarget.style.boxShadow = '0 8px 25px rgba(0,0,0,0.15)';
              }}
              onMouseOut={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 4px 6px rgba(0,0,0,0.1)';
              }}
            >
              <span style={styles.featureIcon}>{feature.icon}</span>
              <h3 style={styles.featureTitle}>{feature.title}</h3>
              <p style={styles.featureDescription}>{feature.description}</p>
              <ul style={styles.featureList}>
                {feature.items.map((item, itemIndex) => (
                  <li key={itemIndex} style={styles.featureListItem}>
                    <span style={styles.checkmark}>✓</span>
                    {item}
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}

export default FeaturesSection;
