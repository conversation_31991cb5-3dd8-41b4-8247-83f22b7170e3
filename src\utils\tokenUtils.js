/**
 * Generates a cryptographically secure token for signup links
 * @param {number} length - Length of the token (default: 32)
 * @returns {string} - Secure random token
 */
export function generateSecureToken(length = 32) {
  const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_';
  const array = new Uint8Array(length);
  
  // Use crypto.getRandomValues for cryptographically secure random numbers
  if (typeof window !== 'undefined' && window.crypto && window.crypto.getRandomValues) {
    window.crypto.getRandomValues(array);
  } else if (typeof global !== 'undefined' && global.crypto && global.crypto.getRandomValues) {
    global.crypto.getRandomValues(array);
  } else {
    // Fallback for environments without crypto.getRandomValues
    for (let i = 0; i < length; i++) {
      array[i] = Math.floor(Math.random() * 256);
    }
  }
  
  return Array.from(array, byte => charset[byte % charset.length]).join('');
}

/**
 * Validates token format
 * @param {string} token - Token to validate
 * @returns {boolean} - True if token format is valid
 */
export function isValidTokenFormat(token) {
  if (!token || typeof token !== 'string') {
    return false;
  }
  
  // Token should be alphanumeric with hyphens and underscores, 16-64 characters
  const tokenRegex = /^[A-Za-z0-9_-]{16,64}$/;
  return tokenRegex.test(token);
}

/**
 * Creates a signup URL with the given token
 * @param {string} token - The signup token
 * @param {string} baseUrl - Base URL of the application (optional)
 * @returns {string} - Complete signup URL
 */
export function createSignupUrl(token, baseUrl = window?.location?.origin || '') {
  return `${baseUrl}/signup/${encodeURIComponent(token)}`;
}

/**
 * Extracts token from URL parameters
 * @param {string} url - URL to extract token from
 * @returns {string|null} - Extracted token or null if not found
 */
export function extractTokenFromUrl(url) {
  try {
    const urlObj = new URL(url);
    const pathParts = urlObj.pathname.split('/');
    
    // Look for /signup/{token} pattern
    const signupIndex = pathParts.indexOf('signup');
    if (signupIndex !== -1 && pathParts.length > signupIndex + 1) {
      const token = decodeURIComponent(pathParts[signupIndex + 1]);
      return isValidTokenFormat(token) ? token : null;
    }
    
    return null;
  } catch (error) {
    console.error('Error extracting token from URL:', error);
    return null;
  }
}

/**
 * Calculates token expiration time
 * @param {number} hoursFromNow - Hours from now when token should expire (default: 24)
 * @returns {Date} - Expiration date
 */
export function calculateTokenExpiration(hoursFromNow = 24) {
  const expiration = new Date();
  expiration.setHours(expiration.getHours() + hoursFromNow);
  return expiration;
}

/**
 * Checks if a token has expired
 * @param {string|Date} expirationDate - Expiration date as string or Date object
 * @returns {boolean} - True if token has expired
 */
export function isTokenExpired(expirationDate) {
  try {
    const expDate = typeof expirationDate === 'string' ? new Date(expirationDate) : expirationDate;
    return expDate < new Date();
  } catch (error) {
    console.error('Error checking token expiration:', error);
    return true; // Assume expired if we can't parse the date
  }
}

/**
 * Formats time remaining until token expiration
 * @param {string|Date} expirationDate - Expiration date
 * @returns {string} - Human-readable time remaining
 */
export function formatTimeRemaining(expirationDate) {
  try {
    const expDate = typeof expirationDate === 'string' ? new Date(expirationDate) : expirationDate;
    const now = new Date();
    const diffMs = expDate - now;
    
    if (diffMs <= 0) {
      return 'Expired';
    }
    
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    
    if (diffHours > 0) {
      return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ${diffMinutes} minute${diffMinutes !== 1 ? 's' : ''}`;
    } else {
      return `${diffMinutes} minute${diffMinutes !== 1 ? 's' : ''}`;
    }
  } catch (error) {
    console.error('Error formatting time remaining:', error);
    return 'Unknown';
  }
}
