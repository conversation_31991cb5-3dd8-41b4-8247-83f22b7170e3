(function(){const i=document.createElement("link").relList;if(i&&i.supports&&i.supports("modulepreload"))return;for(const d of document.querySelectorAll('link[rel="modulepreload"]'))s(d);new MutationObserver(d=>{for(const y of d)if(y.type==="childList")for(const A of y.addedNodes)A.tagName==="LINK"&&A.rel==="modulepreload"&&s(A)}).observe(document,{childList:!0,subtree:!0});function r(d){const y={};return d.integrity&&(y.integrity=d.integrity),d.referrerPolicy&&(y.referrerPolicy=d.referrerPolicy),d.crossOrigin==="use-credentials"?y.credentials="include":d.crossOrigin==="anonymous"?y.credentials="omit":y.credentials="same-origin",y}function s(d){if(d.ep)return;d.ep=!0;const y=r(d);fetch(d.href,y)}})();function Kv(c){return c&&c.__esModule&&Object.prototype.hasOwnProperty.call(c,"default")?c.default:c}var hr={exports:{}},Aa={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ud;function Jv(){if(Ud)return Aa;Ud=1;var c=Symbol.for("react.transitional.element"),i=Symbol.for("react.fragment");function r(s,d,y){var A=null;if(y!==void 0&&(A=""+y),d.key!==void 0&&(A=""+d.key),"key"in d){y={};for(var R in d)R!=="key"&&(y[R]=d[R])}else y=d;return d=y.ref,{$$typeof:c,type:s,key:A,ref:d!==void 0?d:null,props:y}}return Aa.Fragment=i,Aa.jsx=r,Aa.jsxs=r,Aa}var Hd;function kv(){return Hd||(Hd=1,hr.exports=Jv()),hr.exports}var H=kv(),gr={exports:{}},I={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var qd;function Iv(){if(qd)return I;qd=1;var c=Symbol.for("react.transitional.element"),i=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),s=Symbol.for("react.strict_mode"),d=Symbol.for("react.profiler"),y=Symbol.for("react.consumer"),A=Symbol.for("react.context"),R=Symbol.for("react.forward_ref"),C=Symbol.for("react.suspense"),b=Symbol.for("react.memo"),z=Symbol.for("react.lazy"),L=Symbol.iterator;function G(g){return g===null||typeof g!="object"?null:(g=L&&g[L]||g["@@iterator"],typeof g=="function"?g:null)}var B={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},nt=Object.assign,k={};function lt(g,x,q){this.props=g,this.context=x,this.refs=k,this.updater=q||B}lt.prototype.isReactComponent={},lt.prototype.setState=function(g,x){if(typeof g!="object"&&typeof g!="function"&&g!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,g,x,"setState")},lt.prototype.forceUpdate=function(g){this.updater.enqueueForceUpdate(this,g,"forceUpdate")};function yt(){}yt.prototype=lt.prototype;function st(g,x,q){this.props=g,this.context=x,this.refs=k,this.updater=q||B}var mt=st.prototype=new yt;mt.constructor=st,nt(mt,lt.prototype),mt.isPureReactComponent=!0;var Yt=Array.isArray,$={H:null,A:null,T:null,S:null,V:null},Mt=Object.prototype.hasOwnProperty;function Gt(g,x,q,N,w,ut){return q=ut.ref,{$$typeof:c,type:g,key:x,ref:q!==void 0?q:null,props:ut}}function Vt(g,x){return Gt(g.type,x,void 0,void 0,void 0,g.props)}function Oe(g){return typeof g=="object"&&g!==null&&g.$$typeof===c}function Gl(g){var x={"=":"=0",":":"=2"};return"$"+g.replace(/[=:]/g,function(q){return x[q]})}var xe=/\/+/g;function wt(g,x){return typeof g=="object"&&g!==null&&g.key!=null?Gl(""+g.key):x.toString(36)}function pl(){}function Sl(g){switch(g.status){case"fulfilled":return g.value;case"rejected":throw g.reason;default:switch(typeof g.status=="string"?g.then(pl,pl):(g.status="pending",g.then(function(x){g.status==="pending"&&(g.status="fulfilled",g.value=x)},function(x){g.status==="pending"&&(g.status="rejected",g.reason=x)})),g.status){case"fulfilled":return g.value;case"rejected":throw g.reason}}throw g}function Xt(g,x,q,N,w){var ut=typeof g;(ut==="undefined"||ut==="boolean")&&(g=null);var K=!1;if(g===null)K=!0;else switch(ut){case"bigint":case"string":case"number":K=!0;break;case"object":switch(g.$$typeof){case c:case i:K=!0;break;case z:return K=g._init,Xt(K(g._payload),x,q,N,w)}}if(K)return w=w(g),K=N===""?"."+wt(g,0):N,Yt(w)?(q="",K!=null&&(q=K.replace(xe,"$&/")+"/"),Xt(w,x,q,"",function(Je){return Je})):w!=null&&(Oe(w)&&(w=Vt(w,q+(w.key==null||g&&g.key===w.key?"":(""+w.key).replace(xe,"$&/")+"/")+K)),x.push(w)),1;K=0;var Ft=N===""?".":N+":";if(Yt(g))for(var St=0;St<g.length;St++)N=g[St],ut=Ft+wt(N,St),K+=Xt(N,x,q,ut,w);else if(St=G(g),typeof St=="function")for(g=St.call(g),St=0;!(N=g.next()).done;)N=N.value,ut=Ft+wt(N,St++),K+=Xt(N,x,q,ut,w);else if(ut==="object"){if(typeof g.then=="function")return Xt(Sl(g),x,q,N,w);throw x=String(g),Error("Objects are not valid as a React child (found: "+(x==="[object Object]"?"object with keys {"+Object.keys(g).join(", ")+"}":x)+"). If you meant to render a collection of children, use an array instead.")}return K}function E(g,x,q){if(g==null)return g;var N=[],w=0;return Xt(g,N,"","",function(ut){return x.call(q,ut,w++)}),N}function U(g){if(g._status===-1){var x=g._result;x=x(),x.then(function(q){(g._status===0||g._status===-1)&&(g._status=1,g._result=q)},function(q){(g._status===0||g._status===-1)&&(g._status=2,g._result=q)}),g._status===-1&&(g._status=0,g._result=x)}if(g._status===1)return g._result.default;throw g._result}var Z=typeof reportError=="function"?reportError:function(g){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var x=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof g=="object"&&g!==null&&typeof g.message=="string"?String(g.message):String(g),error:g});if(!window.dispatchEvent(x))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",g);return}console.error(g)};function gt(){}return I.Children={map:E,forEach:function(g,x,q){E(g,function(){x.apply(this,arguments)},q)},count:function(g){var x=0;return E(g,function(){x++}),x},toArray:function(g){return E(g,function(x){return x})||[]},only:function(g){if(!Oe(g))throw Error("React.Children.only expected to receive a single React element child.");return g}},I.Component=lt,I.Fragment=r,I.Profiler=d,I.PureComponent=st,I.StrictMode=s,I.Suspense=C,I.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=$,I.__COMPILER_RUNTIME={__proto__:null,c:function(g){return $.H.useMemoCache(g)}},I.cache=function(g){return function(){return g.apply(null,arguments)}},I.cloneElement=function(g,x,q){if(g==null)throw Error("The argument must be a React element, but you passed "+g+".");var N=nt({},g.props),w=g.key,ut=void 0;if(x!=null)for(K in x.ref!==void 0&&(ut=void 0),x.key!==void 0&&(w=""+x.key),x)!Mt.call(x,K)||K==="key"||K==="__self"||K==="__source"||K==="ref"&&x.ref===void 0||(N[K]=x[K]);var K=arguments.length-2;if(K===1)N.children=q;else if(1<K){for(var Ft=Array(K),St=0;St<K;St++)Ft[St]=arguments[St+2];N.children=Ft}return Gt(g.type,w,void 0,void 0,ut,N)},I.createContext=function(g){return g={$$typeof:A,_currentValue:g,_currentValue2:g,_threadCount:0,Provider:null,Consumer:null},g.Provider=g,g.Consumer={$$typeof:y,_context:g},g},I.createElement=function(g,x,q){var N,w={},ut=null;if(x!=null)for(N in x.key!==void 0&&(ut=""+x.key),x)Mt.call(x,N)&&N!=="key"&&N!=="__self"&&N!=="__source"&&(w[N]=x[N]);var K=arguments.length-2;if(K===1)w.children=q;else if(1<K){for(var Ft=Array(K),St=0;St<K;St++)Ft[St]=arguments[St+2];w.children=Ft}if(g&&g.defaultProps)for(N in K=g.defaultProps,K)w[N]===void 0&&(w[N]=K[N]);return Gt(g,ut,void 0,void 0,null,w)},I.createRef=function(){return{current:null}},I.forwardRef=function(g){return{$$typeof:R,render:g}},I.isValidElement=Oe,I.lazy=function(g){return{$$typeof:z,_payload:{_status:-1,_result:g},_init:U}},I.memo=function(g,x){return{$$typeof:b,type:g,compare:x===void 0?null:x}},I.startTransition=function(g){var x=$.T,q={};$.T=q;try{var N=g(),w=$.S;w!==null&&w(q,N),typeof N=="object"&&N!==null&&typeof N.then=="function"&&N.then(gt,Z)}catch(ut){Z(ut)}finally{$.T=x}},I.unstable_useCacheRefresh=function(){return $.H.useCacheRefresh()},I.use=function(g){return $.H.use(g)},I.useActionState=function(g,x,q){return $.H.useActionState(g,x,q)},I.useCallback=function(g,x){return $.H.useCallback(g,x)},I.useContext=function(g){return $.H.useContext(g)},I.useDebugValue=function(){},I.useDeferredValue=function(g,x){return $.H.useDeferredValue(g,x)},I.useEffect=function(g,x,q){var N=$.H;if(typeof q=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return N.useEffect(g,x)},I.useId=function(){return $.H.useId()},I.useImperativeHandle=function(g,x,q){return $.H.useImperativeHandle(g,x,q)},I.useInsertionEffect=function(g,x){return $.H.useInsertionEffect(g,x)},I.useLayoutEffect=function(g,x){return $.H.useLayoutEffect(g,x)},I.useMemo=function(g,x){return $.H.useMemo(g,x)},I.useOptimistic=function(g,x){return $.H.useOptimistic(g,x)},I.useReducer=function(g,x,q){return $.H.useReducer(g,x,q)},I.useRef=function(g){return $.H.useRef(g)},I.useState=function(g){return $.H.useState(g)},I.useSyncExternalStore=function(g,x,q){return $.H.useSyncExternalStore(g,x,q)},I.useTransition=function(){return $.H.useTransition()},I.version="19.1.0",I}var jd;function zr(){return jd||(jd=1,gr.exports=Iv()),gr.exports}var P=zr(),vr={exports:{}},Ma={},yr={exports:{}},mr={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Bd;function Wv(){return Bd||(Bd=1,function(c){function i(E,U){var Z=E.length;E.push(U);t:for(;0<Z;){var gt=Z-1>>>1,g=E[gt];if(0<d(g,U))E[gt]=U,E[Z]=g,Z=gt;else break t}}function r(E){return E.length===0?null:E[0]}function s(E){if(E.length===0)return null;var U=E[0],Z=E.pop();if(Z!==U){E[0]=Z;t:for(var gt=0,g=E.length,x=g>>>1;gt<x;){var q=2*(gt+1)-1,N=E[q],w=q+1,ut=E[w];if(0>d(N,Z))w<g&&0>d(ut,N)?(E[gt]=ut,E[w]=Z,gt=w):(E[gt]=N,E[q]=Z,gt=q);else if(w<g&&0>d(ut,Z))E[gt]=ut,E[w]=Z,gt=w;else break t}}return U}function d(E,U){var Z=E.sortIndex-U.sortIndex;return Z!==0?Z:E.id-U.id}if(c.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var y=performance;c.unstable_now=function(){return y.now()}}else{var A=Date,R=A.now();c.unstable_now=function(){return A.now()-R}}var C=[],b=[],z=1,L=null,G=3,B=!1,nt=!1,k=!1,lt=!1,yt=typeof setTimeout=="function"?setTimeout:null,st=typeof clearTimeout=="function"?clearTimeout:null,mt=typeof setImmediate<"u"?setImmediate:null;function Yt(E){for(var U=r(b);U!==null;){if(U.callback===null)s(b);else if(U.startTime<=E)s(b),U.sortIndex=U.expirationTime,i(C,U);else break;U=r(b)}}function $(E){if(k=!1,Yt(E),!nt)if(r(C)!==null)nt=!0,Mt||(Mt=!0,wt());else{var U=r(b);U!==null&&Xt($,U.startTime-E)}}var Mt=!1,Gt=-1,Vt=5,Oe=-1;function Gl(){return lt?!0:!(c.unstable_now()-Oe<Vt)}function xe(){if(lt=!1,Mt){var E=c.unstable_now();Oe=E;var U=!0;try{t:{nt=!1,k&&(k=!1,st(Gt),Gt=-1),B=!0;var Z=G;try{e:{for(Yt(E),L=r(C);L!==null&&!(L.expirationTime>E&&Gl());){var gt=L.callback;if(typeof gt=="function"){L.callback=null,G=L.priorityLevel;var g=gt(L.expirationTime<=E);if(E=c.unstable_now(),typeof g=="function"){L.callback=g,Yt(E),U=!0;break e}L===r(C)&&s(C),Yt(E)}else s(C);L=r(C)}if(L!==null)U=!0;else{var x=r(b);x!==null&&Xt($,x.startTime-E),U=!1}}break t}finally{L=null,G=Z,B=!1}U=void 0}}finally{U?wt():Mt=!1}}}var wt;if(typeof mt=="function")wt=function(){mt(xe)};else if(typeof MessageChannel<"u"){var pl=new MessageChannel,Sl=pl.port2;pl.port1.onmessage=xe,wt=function(){Sl.postMessage(null)}}else wt=function(){yt(xe,0)};function Xt(E,U){Gt=yt(function(){E(c.unstable_now())},U)}c.unstable_IdlePriority=5,c.unstable_ImmediatePriority=1,c.unstable_LowPriority=4,c.unstable_NormalPriority=3,c.unstable_Profiling=null,c.unstable_UserBlockingPriority=2,c.unstable_cancelCallback=function(E){E.callback=null},c.unstable_forceFrameRate=function(E){0>E||125<E?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):Vt=0<E?Math.floor(1e3/E):5},c.unstable_getCurrentPriorityLevel=function(){return G},c.unstable_next=function(E){switch(G){case 1:case 2:case 3:var U=3;break;default:U=G}var Z=G;G=U;try{return E()}finally{G=Z}},c.unstable_requestPaint=function(){lt=!0},c.unstable_runWithPriority=function(E,U){switch(E){case 1:case 2:case 3:case 4:case 5:break;default:E=3}var Z=G;G=E;try{return U()}finally{G=Z}},c.unstable_scheduleCallback=function(E,U,Z){var gt=c.unstable_now();switch(typeof Z=="object"&&Z!==null?(Z=Z.delay,Z=typeof Z=="number"&&0<Z?gt+Z:gt):Z=gt,E){case 1:var g=-1;break;case 2:g=250;break;case 5:g=1073741823;break;case 4:g=1e4;break;default:g=5e3}return g=Z+g,E={id:z++,callback:U,priorityLevel:E,startTime:Z,expirationTime:g,sortIndex:-1},Z>gt?(E.sortIndex=Z,i(b,E),r(C)===null&&E===r(b)&&(k?(st(Gt),Gt=-1):k=!0,Xt($,Z-gt))):(E.sortIndex=g,i(C,E),nt||B||(nt=!0,Mt||(Mt=!0,wt()))),E},c.unstable_shouldYield=Gl,c.unstable_wrapCallback=function(E){var U=G;return function(){var Z=G;G=U;try{return E.apply(this,arguments)}finally{G=Z}}}}(mr)),mr}var Yd;function $v(){return Yd||(Yd=1,yr.exports=Wv()),yr.exports}var pr={exports:{}},Zt={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Gd;function Fv(){if(Gd)return Zt;Gd=1;var c=zr();function i(C){var b="https://react.dev/errors/"+C;if(1<arguments.length){b+="?args[]="+encodeURIComponent(arguments[1]);for(var z=2;z<arguments.length;z++)b+="&args[]="+encodeURIComponent(arguments[z])}return"Minified React error #"+C+"; visit "+b+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function r(){}var s={d:{f:r,r:function(){throw Error(i(522))},D:r,C:r,L:r,m:r,X:r,S:r,M:r},p:0,findDOMNode:null},d=Symbol.for("react.portal");function y(C,b,z){var L=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:d,key:L==null?null:""+L,children:C,containerInfo:b,implementation:z}}var A=c.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function R(C,b){if(C==="font")return"";if(typeof b=="string")return b==="use-credentials"?b:""}return Zt.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=s,Zt.createPortal=function(C,b){var z=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!b||b.nodeType!==1&&b.nodeType!==9&&b.nodeType!==11)throw Error(i(299));return y(C,b,null,z)},Zt.flushSync=function(C){var b=A.T,z=s.p;try{if(A.T=null,s.p=2,C)return C()}finally{A.T=b,s.p=z,s.d.f()}},Zt.preconnect=function(C,b){typeof C=="string"&&(b?(b=b.crossOrigin,b=typeof b=="string"?b==="use-credentials"?b:"":void 0):b=null,s.d.C(C,b))},Zt.prefetchDNS=function(C){typeof C=="string"&&s.d.D(C)},Zt.preinit=function(C,b){if(typeof C=="string"&&b&&typeof b.as=="string"){var z=b.as,L=R(z,b.crossOrigin),G=typeof b.integrity=="string"?b.integrity:void 0,B=typeof b.fetchPriority=="string"?b.fetchPriority:void 0;z==="style"?s.d.S(C,typeof b.precedence=="string"?b.precedence:void 0,{crossOrigin:L,integrity:G,fetchPriority:B}):z==="script"&&s.d.X(C,{crossOrigin:L,integrity:G,fetchPriority:B,nonce:typeof b.nonce=="string"?b.nonce:void 0})}},Zt.preinitModule=function(C,b){if(typeof C=="string")if(typeof b=="object"&&b!==null){if(b.as==null||b.as==="script"){var z=R(b.as,b.crossOrigin);s.d.M(C,{crossOrigin:z,integrity:typeof b.integrity=="string"?b.integrity:void 0,nonce:typeof b.nonce=="string"?b.nonce:void 0})}}else b==null&&s.d.M(C)},Zt.preload=function(C,b){if(typeof C=="string"&&typeof b=="object"&&b!==null&&typeof b.as=="string"){var z=b.as,L=R(z,b.crossOrigin);s.d.L(C,z,{crossOrigin:L,integrity:typeof b.integrity=="string"?b.integrity:void 0,nonce:typeof b.nonce=="string"?b.nonce:void 0,type:typeof b.type=="string"?b.type:void 0,fetchPriority:typeof b.fetchPriority=="string"?b.fetchPriority:void 0,referrerPolicy:typeof b.referrerPolicy=="string"?b.referrerPolicy:void 0,imageSrcSet:typeof b.imageSrcSet=="string"?b.imageSrcSet:void 0,imageSizes:typeof b.imageSizes=="string"?b.imageSizes:void 0,media:typeof b.media=="string"?b.media:void 0})}},Zt.preloadModule=function(C,b){if(typeof C=="string")if(b){var z=R(b.as,b.crossOrigin);s.d.m(C,{as:typeof b.as=="string"&&b.as!=="script"?b.as:void 0,crossOrigin:z,integrity:typeof b.integrity=="string"?b.integrity:void 0})}else s.d.m(C)},Zt.requestFormReset=function(C){s.d.r(C)},Zt.unstable_batchedUpdates=function(C,b){return C(b)},Zt.useFormState=function(C,b,z){return A.H.useFormState(C,b,z)},Zt.useFormStatus=function(){return A.H.useHostTransitionStatus()},Zt.version="19.1.0",Zt}var wd;function Pv(){if(wd)return pr.exports;wd=1;function c(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(c)}catch(i){console.error(i)}}return c(),pr.exports=Fv(),pr.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Xd;function ty(){if(Xd)return Ma;Xd=1;var c=$v(),i=zr(),r=Pv();function s(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var l=2;l<arguments.length;l++)e+="&args[]="+encodeURIComponent(arguments[l])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function d(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function y(t){var e=t,l=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(l=e.return),t=e.return;while(t)}return e.tag===3?l:null}function A(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function R(t){if(y(t)!==t)throw Error(s(188))}function C(t){var e=t.alternate;if(!e){if(e=y(t),e===null)throw Error(s(188));return e!==t?null:t}for(var l=t,n=e;;){var a=l.return;if(a===null)break;var u=a.alternate;if(u===null){if(n=a.return,n!==null){l=n;continue}break}if(a.child===u.child){for(u=a.child;u;){if(u===l)return R(a),t;if(u===n)return R(a),e;u=u.sibling}throw Error(s(188))}if(l.return!==n.return)l=a,n=u;else{for(var f=!1,o=a.child;o;){if(o===l){f=!0,l=a,n=u;break}if(o===n){f=!0,n=a,l=u;break}o=o.sibling}if(!f){for(o=u.child;o;){if(o===l){f=!0,l=u,n=a;break}if(o===n){f=!0,n=u,l=a;break}o=o.sibling}if(!f)throw Error(s(189))}}if(l.alternate!==n)throw Error(s(190))}if(l.tag!==3)throw Error(s(188));return l.stateNode.current===l?t:e}function b(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=b(t),e!==null)return e;t=t.sibling}return null}var z=Object.assign,L=Symbol.for("react.element"),G=Symbol.for("react.transitional.element"),B=Symbol.for("react.portal"),nt=Symbol.for("react.fragment"),k=Symbol.for("react.strict_mode"),lt=Symbol.for("react.profiler"),yt=Symbol.for("react.provider"),st=Symbol.for("react.consumer"),mt=Symbol.for("react.context"),Yt=Symbol.for("react.forward_ref"),$=Symbol.for("react.suspense"),Mt=Symbol.for("react.suspense_list"),Gt=Symbol.for("react.memo"),Vt=Symbol.for("react.lazy"),Oe=Symbol.for("react.activity"),Gl=Symbol.for("react.memo_cache_sentinel"),xe=Symbol.iterator;function wt(t){return t===null||typeof t!="object"?null:(t=xe&&t[xe]||t["@@iterator"],typeof t=="function"?t:null)}var pl=Symbol.for("react.client.reference");function Sl(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===pl?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case nt:return"Fragment";case lt:return"Profiler";case k:return"StrictMode";case $:return"Suspense";case Mt:return"SuspenseList";case Oe:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case B:return"Portal";case mt:return(t.displayName||"Context")+".Provider";case st:return(t._context.displayName||"Context")+".Consumer";case Yt:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case Gt:return e=t.displayName||null,e!==null?e:Sl(t.type)||"Memo";case Vt:e=t._payload,t=t._init;try{return Sl(t(e))}catch{}}return null}var Xt=Array.isArray,E=i.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,U=r.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,Z={pending:!1,data:null,method:null,action:null},gt=[],g=-1;function x(t){return{current:t}}function q(t){0>g||(t.current=gt[g],gt[g]=null,g--)}function N(t,e){g++,gt[g]=t.current,t.current=e}var w=x(null),ut=x(null),K=x(null),Ft=x(null);function St(t,e){switch(N(K,e),N(ut,t),N(w,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?cd(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=cd(e),t=rd(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}q(w),N(w,t)}function Je(){q(w),q(ut),q(K)}function Fu(t){t.memoizedState!==null&&N(Ft,t);var e=w.current,l=rd(e,t.type);e!==l&&(N(ut,t),N(w,l))}function xa(t){ut.current===t&&(q(w),q(ut)),Ft.current===t&&(q(Ft),ba._currentValue=Z)}var Pu=Object.prototype.hasOwnProperty,ti=c.unstable_scheduleCallback,ei=c.unstable_cancelCallback,Eh=c.unstable_shouldYield,Ah=c.unstable_requestPaint,Ee=c.unstable_now,Mh=c.unstable_getCurrentPriorityLevel,Gr=c.unstable_ImmediatePriority,wr=c.unstable_UserBlockingPriority,za=c.unstable_NormalPriority,Ch=c.unstable_LowPriority,Xr=c.unstable_IdlePriority,_h=c.log,Rh=c.unstable_setDisableYieldValue,_n=null,Pt=null;function ke(t){if(typeof _h=="function"&&Rh(t),Pt&&typeof Pt.setStrictMode=="function")try{Pt.setStrictMode(_n,t)}catch{}}var te=Math.clz32?Math.clz32:Nh,xh=Math.log,zh=Math.LN2;function Nh(t){return t>>>=0,t===0?32:31-(xh(t)/zh|0)|0}var Na=256,Ua=4194304;function bl(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function Ha(t,e,l){var n=t.pendingLanes;if(n===0)return 0;var a=0,u=t.suspendedLanes,f=t.pingedLanes;t=t.warmLanes;var o=n&134217727;return o!==0?(n=o&~u,n!==0?a=bl(n):(f&=o,f!==0?a=bl(f):l||(l=o&~t,l!==0&&(a=bl(l))))):(o=n&~u,o!==0?a=bl(o):f!==0?a=bl(f):l||(l=n&~t,l!==0&&(a=bl(l)))),a===0?0:e!==0&&e!==a&&(e&u)===0&&(u=a&-a,l=e&-e,u>=l||u===32&&(l&4194048)!==0)?e:a}function Rn(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function Uh(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Lr(){var t=Na;return Na<<=1,(Na&4194048)===0&&(Na=256),t}function Qr(){var t=Ua;return Ua<<=1,(Ua&62914560)===0&&(Ua=4194304),t}function li(t){for(var e=[],l=0;31>l;l++)e.push(t);return e}function xn(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function Hh(t,e,l,n,a,u){var f=t.pendingLanes;t.pendingLanes=l,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=l,t.entangledLanes&=l,t.errorRecoveryDisabledLanes&=l,t.shellSuspendCounter=0;var o=t.entanglements,h=t.expirationTimes,S=t.hiddenUpdates;for(l=f&~l;0<l;){var O=31-te(l),_=1<<O;o[O]=0,h[O]=-1;var T=S[O];if(T!==null)for(S[O]=null,O=0;O<T.length;O++){var D=T[O];D!==null&&(D.lane&=-536870913)}l&=~_}n!==0&&Zr(t,n,0),u!==0&&a===0&&t.tag!==0&&(t.suspendedLanes|=u&~(f&~e))}function Zr(t,e,l){t.pendingLanes|=e,t.suspendedLanes&=~e;var n=31-te(e);t.entangledLanes|=e,t.entanglements[n]=t.entanglements[n]|1073741824|l&4194090}function Vr(t,e){var l=t.entangledLanes|=e;for(t=t.entanglements;l;){var n=31-te(l),a=1<<n;a&e|t[n]&e&&(t[n]|=e),l&=~a}}function ni(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function ai(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function Kr(){var t=U.p;return t!==0?t:(t=window.event,t===void 0?32:Cd(t.type))}function qh(t,e){var l=U.p;try{return U.p=t,e()}finally{U.p=l}}var Ie=Math.random().toString(36).slice(2),Lt="__reactFiber$"+Ie,Jt="__reactProps$"+Ie,wl="__reactContainer$"+Ie,ui="__reactEvents$"+Ie,jh="__reactListeners$"+Ie,Bh="__reactHandles$"+Ie,Jr="__reactResources$"+Ie,zn="__reactMarker$"+Ie;function ii(t){delete t[Lt],delete t[Jt],delete t[ui],delete t[jh],delete t[Bh]}function Xl(t){var e=t[Lt];if(e)return e;for(var l=t.parentNode;l;){if(e=l[wl]||l[Lt]){if(l=e.alternate,e.child!==null||l!==null&&l.child!==null)for(t=dd(t);t!==null;){if(l=t[Lt])return l;t=dd(t)}return e}t=l,l=t.parentNode}return null}function Ll(t){if(t=t[Lt]||t[wl]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function Nn(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(s(33))}function Ql(t){var e=t[Jr];return e||(e=t[Jr]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function zt(t){t[zn]=!0}var kr=new Set,Ir={};function Tl(t,e){Zl(t,e),Zl(t+"Capture",e)}function Zl(t,e){for(Ir[t]=e,t=0;t<e.length;t++)kr.add(e[t])}var Yh=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Wr={},$r={};function Gh(t){return Pu.call($r,t)?!0:Pu.call(Wr,t)?!1:Yh.test(t)?$r[t]=!0:(Wr[t]=!0,!1)}function qa(t,e,l){if(Gh(e))if(l===null)t.removeAttribute(e);else{switch(typeof l){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var n=e.toLowerCase().slice(0,5);if(n!=="data-"&&n!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+l)}}function ja(t,e,l){if(l===null)t.removeAttribute(e);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+l)}}function ze(t,e,l,n){if(n===null)t.removeAttribute(l);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(l);return}t.setAttributeNS(e,l,""+n)}}var ci,Fr;function Vl(t){if(ci===void 0)try{throw Error()}catch(l){var e=l.stack.trim().match(/\n( *(at )?)/);ci=e&&e[1]||"",Fr=-1<l.stack.indexOf(`
    at`)?" (<anonymous>)":-1<l.stack.indexOf("@")?"@unknown:0:0":""}return`
`+ci+t+Fr}var ri=!1;function fi(t,e){if(!t||ri)return"";ri=!0;var l=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var n={DetermineComponentFrameRoot:function(){try{if(e){var _=function(){throw Error()};if(Object.defineProperty(_.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(_,[])}catch(D){var T=D}Reflect.construct(t,[],_)}else{try{_.call()}catch(D){T=D}t.call(_.prototype)}}else{try{throw Error()}catch(D){T=D}(_=t())&&typeof _.catch=="function"&&_.catch(function(){})}}catch(D){if(D&&T&&typeof D.stack=="string")return[D.stack,T.stack]}return[null,null]}};n.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var a=Object.getOwnPropertyDescriptor(n.DetermineComponentFrameRoot,"name");a&&a.configurable&&Object.defineProperty(n.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var u=n.DetermineComponentFrameRoot(),f=u[0],o=u[1];if(f&&o){var h=f.split(`
`),S=o.split(`
`);for(a=n=0;n<h.length&&!h[n].includes("DetermineComponentFrameRoot");)n++;for(;a<S.length&&!S[a].includes("DetermineComponentFrameRoot");)a++;if(n===h.length||a===S.length)for(n=h.length-1,a=S.length-1;1<=n&&0<=a&&h[n]!==S[a];)a--;for(;1<=n&&0<=a;n--,a--)if(h[n]!==S[a]){if(n!==1||a!==1)do if(n--,a--,0>a||h[n]!==S[a]){var O=`
`+h[n].replace(" at new "," at ");return t.displayName&&O.includes("<anonymous>")&&(O=O.replace("<anonymous>",t.displayName)),O}while(1<=n&&0<=a);break}}}finally{ri=!1,Error.prepareStackTrace=l}return(l=t?t.displayName||t.name:"")?Vl(l):""}function wh(t){switch(t.tag){case 26:case 27:case 5:return Vl(t.type);case 16:return Vl("Lazy");case 13:return Vl("Suspense");case 19:return Vl("SuspenseList");case 0:case 15:return fi(t.type,!1);case 11:return fi(t.type.render,!1);case 1:return fi(t.type,!0);case 31:return Vl("Activity");default:return""}}function Pr(t){try{var e="";do e+=wh(t),t=t.return;while(t);return e}catch(l){return`
Error generating stack: `+l.message+`
`+l.stack}}function re(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function tf(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function Xh(t){var e=tf(t)?"checked":"value",l=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),n=""+t[e];if(!t.hasOwnProperty(e)&&typeof l<"u"&&typeof l.get=="function"&&typeof l.set=="function"){var a=l.get,u=l.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return a.call(this)},set:function(f){n=""+f,u.call(this,f)}}),Object.defineProperty(t,e,{enumerable:l.enumerable}),{getValue:function(){return n},setValue:function(f){n=""+f},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function Ba(t){t._valueTracker||(t._valueTracker=Xh(t))}function ef(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var l=e.getValue(),n="";return t&&(n=tf(t)?t.checked?"true":"false":t.value),t=n,t!==l?(e.setValue(t),!0):!1}function Ya(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var Lh=/[\n"\\]/g;function fe(t){return t.replace(Lh,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function si(t,e,l,n,a,u,f,o){t.name="",f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"?t.type=f:t.removeAttribute("type"),e!=null?f==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+re(e)):t.value!==""+re(e)&&(t.value=""+re(e)):f!=="submit"&&f!=="reset"||t.removeAttribute("value"),e!=null?oi(t,f,re(e)):l!=null?oi(t,f,re(l)):n!=null&&t.removeAttribute("value"),a==null&&u!=null&&(t.defaultChecked=!!u),a!=null&&(t.checked=a&&typeof a!="function"&&typeof a!="symbol"),o!=null&&typeof o!="function"&&typeof o!="symbol"&&typeof o!="boolean"?t.name=""+re(o):t.removeAttribute("name")}function lf(t,e,l,n,a,u,f,o){if(u!=null&&typeof u!="function"&&typeof u!="symbol"&&typeof u!="boolean"&&(t.type=u),e!=null||l!=null){if(!(u!=="submit"&&u!=="reset"||e!=null))return;l=l!=null?""+re(l):"",e=e!=null?""+re(e):l,o||e===t.value||(t.value=e),t.defaultValue=e}n=n??a,n=typeof n!="function"&&typeof n!="symbol"&&!!n,t.checked=o?t.checked:!!n,t.defaultChecked=!!n,f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"&&(t.name=f)}function oi(t,e,l){e==="number"&&Ya(t.ownerDocument)===t||t.defaultValue===""+l||(t.defaultValue=""+l)}function Kl(t,e,l,n){if(t=t.options,e){e={};for(var a=0;a<l.length;a++)e["$"+l[a]]=!0;for(l=0;l<t.length;l++)a=e.hasOwnProperty("$"+t[l].value),t[l].selected!==a&&(t[l].selected=a),a&&n&&(t[l].defaultSelected=!0)}else{for(l=""+re(l),e=null,a=0;a<t.length;a++){if(t[a].value===l){t[a].selected=!0,n&&(t[a].defaultSelected=!0);return}e!==null||t[a].disabled||(e=t[a])}e!==null&&(e.selected=!0)}}function nf(t,e,l){if(e!=null&&(e=""+re(e),e!==t.value&&(t.value=e),l==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=l!=null?""+re(l):""}function af(t,e,l,n){if(e==null){if(n!=null){if(l!=null)throw Error(s(92));if(Xt(n)){if(1<n.length)throw Error(s(93));n=n[0]}l=n}l==null&&(l=""),e=l}l=re(e),t.defaultValue=l,n=t.textContent,n===l&&n!==""&&n!==null&&(t.value=n)}function Jl(t,e){if(e){var l=t.firstChild;if(l&&l===t.lastChild&&l.nodeType===3){l.nodeValue=e;return}}t.textContent=e}var Qh=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function uf(t,e,l){var n=e.indexOf("--")===0;l==null||typeof l=="boolean"||l===""?n?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":n?t.setProperty(e,l):typeof l!="number"||l===0||Qh.has(e)?e==="float"?t.cssFloat=l:t[e]=(""+l).trim():t[e]=l+"px"}function cf(t,e,l){if(e!=null&&typeof e!="object")throw Error(s(62));if(t=t.style,l!=null){for(var n in l)!l.hasOwnProperty(n)||e!=null&&e.hasOwnProperty(n)||(n.indexOf("--")===0?t.setProperty(n,""):n==="float"?t.cssFloat="":t[n]="");for(var a in e)n=e[a],e.hasOwnProperty(a)&&l[a]!==n&&uf(t,a,n)}else for(var u in e)e.hasOwnProperty(u)&&uf(t,u,e[u])}function di(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Zh=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Vh=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Ga(t){return Vh.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var hi=null;function gi(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var kl=null,Il=null;function rf(t){var e=Ll(t);if(e&&(t=e.stateNode)){var l=t[Jt]||null;t:switch(t=e.stateNode,e.type){case"input":if(si(t,l.value,l.defaultValue,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name),e=l.name,l.type==="radio"&&e!=null){for(l=t;l.parentNode;)l=l.parentNode;for(l=l.querySelectorAll('input[name="'+fe(""+e)+'"][type="radio"]'),e=0;e<l.length;e++){var n=l[e];if(n!==t&&n.form===t.form){var a=n[Jt]||null;if(!a)throw Error(s(90));si(n,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name)}}for(e=0;e<l.length;e++)n=l[e],n.form===t.form&&ef(n)}break t;case"textarea":nf(t,l.value,l.defaultValue);break t;case"select":e=l.value,e!=null&&Kl(t,!!l.multiple,e,!1)}}}var vi=!1;function ff(t,e,l){if(vi)return t(e,l);vi=!0;try{var n=t(e);return n}finally{if(vi=!1,(kl!==null||Il!==null)&&(Eu(),kl&&(e=kl,t=Il,Il=kl=null,rf(e),t)))for(e=0;e<t.length;e++)rf(t[e])}}function Un(t,e){var l=t.stateNode;if(l===null)return null;var n=l[Jt]||null;if(n===null)return null;l=n[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(n=!n.disabled)||(t=t.type,n=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!n;break t;default:t=!1}if(t)return null;if(l&&typeof l!="function")throw Error(s(231,e,typeof l));return l}var Ne=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),yi=!1;if(Ne)try{var Hn={};Object.defineProperty(Hn,"passive",{get:function(){yi=!0}}),window.addEventListener("test",Hn,Hn),window.removeEventListener("test",Hn,Hn)}catch{yi=!1}var We=null,mi=null,wa=null;function sf(){if(wa)return wa;var t,e=mi,l=e.length,n,a="value"in We?We.value:We.textContent,u=a.length;for(t=0;t<l&&e[t]===a[t];t++);var f=l-t;for(n=1;n<=f&&e[l-n]===a[u-n];n++);return wa=a.slice(t,1<n?1-n:void 0)}function Xa(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function La(){return!0}function of(){return!1}function kt(t){function e(l,n,a,u,f){this._reactName=l,this._targetInst=a,this.type=n,this.nativeEvent=u,this.target=f,this.currentTarget=null;for(var o in t)t.hasOwnProperty(o)&&(l=t[o],this[o]=l?l(u):u[o]);return this.isDefaultPrevented=(u.defaultPrevented!=null?u.defaultPrevented:u.returnValue===!1)?La:of,this.isPropagationStopped=of,this}return z(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var l=this.nativeEvent;l&&(l.preventDefault?l.preventDefault():typeof l.returnValue!="unknown"&&(l.returnValue=!1),this.isDefaultPrevented=La)},stopPropagation:function(){var l=this.nativeEvent;l&&(l.stopPropagation?l.stopPropagation():typeof l.cancelBubble!="unknown"&&(l.cancelBubble=!0),this.isPropagationStopped=La)},persist:function(){},isPersistent:La}),e}var Dl={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Qa=kt(Dl),qn=z({},Dl,{view:0,detail:0}),Kh=kt(qn),pi,Si,jn,Za=z({},qn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Ti,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==jn&&(jn&&t.type==="mousemove"?(pi=t.screenX-jn.screenX,Si=t.screenY-jn.screenY):Si=pi=0,jn=t),pi)},movementY:function(t){return"movementY"in t?t.movementY:Si}}),df=kt(Za),Jh=z({},Za,{dataTransfer:0}),kh=kt(Jh),Ih=z({},qn,{relatedTarget:0}),bi=kt(Ih),Wh=z({},Dl,{animationName:0,elapsedTime:0,pseudoElement:0}),$h=kt(Wh),Fh=z({},Dl,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),Ph=kt(Fh),tg=z({},Dl,{data:0}),hf=kt(tg),eg={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},lg={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},ng={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function ag(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=ng[t])?!!e[t]:!1}function Ti(){return ag}var ug=z({},qn,{key:function(t){if(t.key){var e=eg[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=Xa(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?lg[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Ti,charCode:function(t){return t.type==="keypress"?Xa(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?Xa(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),ig=kt(ug),cg=z({},Za,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),gf=kt(cg),rg=z({},qn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Ti}),fg=kt(rg),sg=z({},Dl,{propertyName:0,elapsedTime:0,pseudoElement:0}),og=kt(sg),dg=z({},Za,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),hg=kt(dg),gg=z({},Dl,{newState:0,oldState:0}),vg=kt(gg),yg=[9,13,27,32],Di=Ne&&"CompositionEvent"in window,Bn=null;Ne&&"documentMode"in document&&(Bn=document.documentMode);var mg=Ne&&"TextEvent"in window&&!Bn,vf=Ne&&(!Di||Bn&&8<Bn&&11>=Bn),yf=" ",mf=!1;function pf(t,e){switch(t){case"keyup":return yg.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Sf(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var Wl=!1;function pg(t,e){switch(t){case"compositionend":return Sf(e);case"keypress":return e.which!==32?null:(mf=!0,yf);case"textInput":return t=e.data,t===yf&&mf?null:t;default:return null}}function Sg(t,e){if(Wl)return t==="compositionend"||!Di&&pf(t,e)?(t=sf(),wa=mi=We=null,Wl=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return vf&&e.locale!=="ko"?null:e.data;default:return null}}var bg={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function bf(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!bg[t.type]:e==="textarea"}function Tf(t,e,l,n){kl?Il?Il.push(n):Il=[n]:kl=n,e=xu(e,"onChange"),0<e.length&&(l=new Qa("onChange","change",null,l,n),t.push({event:l,listeners:e}))}var Yn=null,Gn=null;function Tg(t){ld(t,0)}function Va(t){var e=Nn(t);if(ef(e))return t}function Df(t,e){if(t==="change")return e}var Of=!1;if(Ne){var Oi;if(Ne){var Ei="oninput"in document;if(!Ei){var Ef=document.createElement("div");Ef.setAttribute("oninput","return;"),Ei=typeof Ef.oninput=="function"}Oi=Ei}else Oi=!1;Of=Oi&&(!document.documentMode||9<document.documentMode)}function Af(){Yn&&(Yn.detachEvent("onpropertychange",Mf),Gn=Yn=null)}function Mf(t){if(t.propertyName==="value"&&Va(Gn)){var e=[];Tf(e,Gn,t,gi(t)),ff(Tg,e)}}function Dg(t,e,l){t==="focusin"?(Af(),Yn=e,Gn=l,Yn.attachEvent("onpropertychange",Mf)):t==="focusout"&&Af()}function Og(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return Va(Gn)}function Eg(t,e){if(t==="click")return Va(e)}function Ag(t,e){if(t==="input"||t==="change")return Va(e)}function Mg(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var ee=typeof Object.is=="function"?Object.is:Mg;function wn(t,e){if(ee(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var l=Object.keys(t),n=Object.keys(e);if(l.length!==n.length)return!1;for(n=0;n<l.length;n++){var a=l[n];if(!Pu.call(e,a)||!ee(t[a],e[a]))return!1}return!0}function Cf(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function _f(t,e){var l=Cf(t);t=0;for(var n;l;){if(l.nodeType===3){if(n=t+l.textContent.length,t<=e&&n>=e)return{node:l,offset:e-t};t=n}t:{for(;l;){if(l.nextSibling){l=l.nextSibling;break t}l=l.parentNode}l=void 0}l=Cf(l)}}function Rf(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?Rf(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function xf(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=Ya(t.document);e instanceof t.HTMLIFrameElement;){try{var l=typeof e.contentWindow.location.href=="string"}catch{l=!1}if(l)t=e.contentWindow;else break;e=Ya(t.document)}return e}function Ai(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var Cg=Ne&&"documentMode"in document&&11>=document.documentMode,$l=null,Mi=null,Xn=null,Ci=!1;function zf(t,e,l){var n=l.window===l?l.document:l.nodeType===9?l:l.ownerDocument;Ci||$l==null||$l!==Ya(n)||(n=$l,"selectionStart"in n&&Ai(n)?n={start:n.selectionStart,end:n.selectionEnd}:(n=(n.ownerDocument&&n.ownerDocument.defaultView||window).getSelection(),n={anchorNode:n.anchorNode,anchorOffset:n.anchorOffset,focusNode:n.focusNode,focusOffset:n.focusOffset}),Xn&&wn(Xn,n)||(Xn=n,n=xu(Mi,"onSelect"),0<n.length&&(e=new Qa("onSelect","select",null,e,l),t.push({event:e,listeners:n}),e.target=$l)))}function Ol(t,e){var l={};return l[t.toLowerCase()]=e.toLowerCase(),l["Webkit"+t]="webkit"+e,l["Moz"+t]="moz"+e,l}var Fl={animationend:Ol("Animation","AnimationEnd"),animationiteration:Ol("Animation","AnimationIteration"),animationstart:Ol("Animation","AnimationStart"),transitionrun:Ol("Transition","TransitionRun"),transitionstart:Ol("Transition","TransitionStart"),transitioncancel:Ol("Transition","TransitionCancel"),transitionend:Ol("Transition","TransitionEnd")},_i={},Nf={};Ne&&(Nf=document.createElement("div").style,"AnimationEvent"in window||(delete Fl.animationend.animation,delete Fl.animationiteration.animation,delete Fl.animationstart.animation),"TransitionEvent"in window||delete Fl.transitionend.transition);function El(t){if(_i[t])return _i[t];if(!Fl[t])return t;var e=Fl[t],l;for(l in e)if(e.hasOwnProperty(l)&&l in Nf)return _i[t]=e[l];return t}var Uf=El("animationend"),Hf=El("animationiteration"),qf=El("animationstart"),_g=El("transitionrun"),Rg=El("transitionstart"),xg=El("transitioncancel"),jf=El("transitionend"),Bf=new Map,Ri="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Ri.push("scrollEnd");function be(t,e){Bf.set(t,e),Tl(e,[t])}var Yf=new WeakMap;function se(t,e){if(typeof t=="object"&&t!==null){var l=Yf.get(t);return l!==void 0?l:(e={value:t,source:e,stack:Pr(e)},Yf.set(t,e),e)}return{value:t,source:e,stack:Pr(e)}}var oe=[],Pl=0,xi=0;function Ka(){for(var t=Pl,e=xi=Pl=0;e<t;){var l=oe[e];oe[e++]=null;var n=oe[e];oe[e++]=null;var a=oe[e];oe[e++]=null;var u=oe[e];if(oe[e++]=null,n!==null&&a!==null){var f=n.pending;f===null?a.next=a:(a.next=f.next,f.next=a),n.pending=a}u!==0&&Gf(l,a,u)}}function Ja(t,e,l,n){oe[Pl++]=t,oe[Pl++]=e,oe[Pl++]=l,oe[Pl++]=n,xi|=n,t.lanes|=n,t=t.alternate,t!==null&&(t.lanes|=n)}function zi(t,e,l,n){return Ja(t,e,l,n),ka(t)}function tn(t,e){return Ja(t,null,null,e),ka(t)}function Gf(t,e,l){t.lanes|=l;var n=t.alternate;n!==null&&(n.lanes|=l);for(var a=!1,u=t.return;u!==null;)u.childLanes|=l,n=u.alternate,n!==null&&(n.childLanes|=l),u.tag===22&&(t=u.stateNode,t===null||t._visibility&1||(a=!0)),t=u,u=u.return;return t.tag===3?(u=t.stateNode,a&&e!==null&&(a=31-te(l),t=u.hiddenUpdates,n=t[a],n===null?t[a]=[e]:n.push(e),e.lane=l|536870912),u):null}function ka(t){if(50<da)throw da=0,Bc=null,Error(s(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var en={};function zg(t,e,l,n){this.tag=t,this.key=l,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=n,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function le(t,e,l,n){return new zg(t,e,l,n)}function Ni(t){return t=t.prototype,!(!t||!t.isReactComponent)}function Ue(t,e){var l=t.alternate;return l===null?(l=le(t.tag,e,t.key,t.mode),l.elementType=t.elementType,l.type=t.type,l.stateNode=t.stateNode,l.alternate=t,t.alternate=l):(l.pendingProps=e,l.type=t.type,l.flags=0,l.subtreeFlags=0,l.deletions=null),l.flags=t.flags&65011712,l.childLanes=t.childLanes,l.lanes=t.lanes,l.child=t.child,l.memoizedProps=t.memoizedProps,l.memoizedState=t.memoizedState,l.updateQueue=t.updateQueue,e=t.dependencies,l.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},l.sibling=t.sibling,l.index=t.index,l.ref=t.ref,l.refCleanup=t.refCleanup,l}function wf(t,e){t.flags&=65011714;var l=t.alternate;return l===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=l.childLanes,t.lanes=l.lanes,t.child=l.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=l.memoizedProps,t.memoizedState=l.memoizedState,t.updateQueue=l.updateQueue,t.type=l.type,e=l.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function Ia(t,e,l,n,a,u){var f=0;if(n=t,typeof t=="function")Ni(t)&&(f=1);else if(typeof t=="string")f=Uv(t,l,w.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case Oe:return t=le(31,l,e,a),t.elementType=Oe,t.lanes=u,t;case nt:return Al(l.children,a,u,e);case k:f=8,a|=24;break;case lt:return t=le(12,l,e,a|2),t.elementType=lt,t.lanes=u,t;case $:return t=le(13,l,e,a),t.elementType=$,t.lanes=u,t;case Mt:return t=le(19,l,e,a),t.elementType=Mt,t.lanes=u,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case yt:case mt:f=10;break t;case st:f=9;break t;case Yt:f=11;break t;case Gt:f=14;break t;case Vt:f=16,n=null;break t}f=29,l=Error(s(130,t===null?"null":typeof t,"")),n=null}return e=le(f,l,e,a),e.elementType=t,e.type=n,e.lanes=u,e}function Al(t,e,l,n){return t=le(7,t,n,e),t.lanes=l,t}function Ui(t,e,l){return t=le(6,t,null,e),t.lanes=l,t}function Hi(t,e,l){return e=le(4,t.children!==null?t.children:[],t.key,e),e.lanes=l,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var ln=[],nn=0,Wa=null,$a=0,de=[],he=0,Ml=null,He=1,qe="";function Cl(t,e){ln[nn++]=$a,ln[nn++]=Wa,Wa=t,$a=e}function Xf(t,e,l){de[he++]=He,de[he++]=qe,de[he++]=Ml,Ml=t;var n=He;t=qe;var a=32-te(n)-1;n&=~(1<<a),l+=1;var u=32-te(e)+a;if(30<u){var f=a-a%5;u=(n&(1<<f)-1).toString(32),n>>=f,a-=f,He=1<<32-te(e)+a|l<<a|n,qe=u+t}else He=1<<u|l<<a|n,qe=t}function qi(t){t.return!==null&&(Cl(t,1),Xf(t,1,0))}function ji(t){for(;t===Wa;)Wa=ln[--nn],ln[nn]=null,$a=ln[--nn],ln[nn]=null;for(;t===Ml;)Ml=de[--he],de[he]=null,qe=de[--he],de[he]=null,He=de[--he],de[he]=null}var Kt=null,Dt=null,ct=!1,_l=null,Ae=!1,Bi=Error(s(519));function Rl(t){var e=Error(s(418,""));throw Zn(se(e,t)),Bi}function Lf(t){var e=t.stateNode,l=t.type,n=t.memoizedProps;switch(e[Lt]=t,e[Jt]=n,l){case"dialog":et("cancel",e),et("close",e);break;case"iframe":case"object":case"embed":et("load",e);break;case"video":case"audio":for(l=0;l<ga.length;l++)et(ga[l],e);break;case"source":et("error",e);break;case"img":case"image":case"link":et("error",e),et("load",e);break;case"details":et("toggle",e);break;case"input":et("invalid",e),lf(e,n.value,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name,!0),Ba(e);break;case"select":et("invalid",e);break;case"textarea":et("invalid",e),af(e,n.value,n.defaultValue,n.children),Ba(e)}l=n.children,typeof l!="string"&&typeof l!="number"&&typeof l!="bigint"||e.textContent===""+l||n.suppressHydrationWarning===!0||id(e.textContent,l)?(n.popover!=null&&(et("beforetoggle",e),et("toggle",e)),n.onScroll!=null&&et("scroll",e),n.onScrollEnd!=null&&et("scrollend",e),n.onClick!=null&&(e.onclick=zu),e=!0):e=!1,e||Rl(t)}function Qf(t){for(Kt=t.return;Kt;)switch(Kt.tag){case 5:case 13:Ae=!1;return;case 27:case 3:Ae=!0;return;default:Kt=Kt.return}}function Ln(t){if(t!==Kt)return!1;if(!ct)return Qf(t),ct=!0,!1;var e=t.tag,l;if((l=e!==3&&e!==27)&&((l=e===5)&&(l=t.type,l=!(l!=="form"&&l!=="button")||Pc(t.type,t.memoizedProps)),l=!l),l&&Dt&&Rl(t),Qf(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(s(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(l=t.data,l==="/$"){if(e===0){Dt=De(t.nextSibling);break t}e--}else l!=="$"&&l!=="$!"&&l!=="$?"||e++;t=t.nextSibling}Dt=null}}else e===27?(e=Dt,dl(t.type)?(t=nr,nr=null,Dt=t):Dt=e):Dt=Kt?De(t.stateNode.nextSibling):null;return!0}function Qn(){Dt=Kt=null,ct=!1}function Zf(){var t=_l;return t!==null&&($t===null?$t=t:$t.push.apply($t,t),_l=null),t}function Zn(t){_l===null?_l=[t]:_l.push(t)}var Yi=x(null),xl=null,je=null;function $e(t,e,l){N(Yi,e._currentValue),e._currentValue=l}function Be(t){t._currentValue=Yi.current,q(Yi)}function Gi(t,e,l){for(;t!==null;){var n=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,n!==null&&(n.childLanes|=e)):n!==null&&(n.childLanes&e)!==e&&(n.childLanes|=e),t===l)break;t=t.return}}function wi(t,e,l,n){var a=t.child;for(a!==null&&(a.return=t);a!==null;){var u=a.dependencies;if(u!==null){var f=a.child;u=u.firstContext;t:for(;u!==null;){var o=u;u=a;for(var h=0;h<e.length;h++)if(o.context===e[h]){u.lanes|=l,o=u.alternate,o!==null&&(o.lanes|=l),Gi(u.return,l,t),n||(f=null);break t}u=o.next}}else if(a.tag===18){if(f=a.return,f===null)throw Error(s(341));f.lanes|=l,u=f.alternate,u!==null&&(u.lanes|=l),Gi(f,l,t),f=null}else f=a.child;if(f!==null)f.return=a;else for(f=a;f!==null;){if(f===t){f=null;break}if(a=f.sibling,a!==null){a.return=f.return,f=a;break}f=f.return}a=f}}function Vn(t,e,l,n){t=null;for(var a=e,u=!1;a!==null;){if(!u){if((a.flags&524288)!==0)u=!0;else if((a.flags&262144)!==0)break}if(a.tag===10){var f=a.alternate;if(f===null)throw Error(s(387));if(f=f.memoizedProps,f!==null){var o=a.type;ee(a.pendingProps.value,f.value)||(t!==null?t.push(o):t=[o])}}else if(a===Ft.current){if(f=a.alternate,f===null)throw Error(s(387));f.memoizedState.memoizedState!==a.memoizedState.memoizedState&&(t!==null?t.push(ba):t=[ba])}a=a.return}t!==null&&wi(e,t,l,n),e.flags|=262144}function Fa(t){for(t=t.firstContext;t!==null;){if(!ee(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function zl(t){xl=t,je=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function Qt(t){return Vf(xl,t)}function Pa(t,e){return xl===null&&zl(t),Vf(t,e)}function Vf(t,e){var l=e._currentValue;if(e={context:e,memoizedValue:l,next:null},je===null){if(t===null)throw Error(s(308));je=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else je=je.next=e;return l}var Ng=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(l,n){t.push(n)}};this.abort=function(){e.aborted=!0,t.forEach(function(l){return l()})}},Ug=c.unstable_scheduleCallback,Hg=c.unstable_NormalPriority,Rt={$$typeof:mt,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Xi(){return{controller:new Ng,data:new Map,refCount:0}}function Kn(t){t.refCount--,t.refCount===0&&Ug(Hg,function(){t.controller.abort()})}var Jn=null,Li=0,an=0,un=null;function qg(t,e){if(Jn===null){var l=Jn=[];Li=0,an=Zc(),un={status:"pending",value:void 0,then:function(n){l.push(n)}}}return Li++,e.then(Kf,Kf),e}function Kf(){if(--Li===0&&Jn!==null){un!==null&&(un.status="fulfilled");var t=Jn;Jn=null,an=0,un=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function jg(t,e){var l=[],n={status:"pending",value:null,reason:null,then:function(a){l.push(a)}};return t.then(function(){n.status="fulfilled",n.value=e;for(var a=0;a<l.length;a++)(0,l[a])(e)},function(a){for(n.status="rejected",n.reason=a,a=0;a<l.length;a++)(0,l[a])(void 0)}),n}var Jf=E.S;E.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&qg(t,e),Jf!==null&&Jf(t,e)};var Nl=x(null);function Qi(){var t=Nl.current;return t!==null?t:pt.pooledCache}function tu(t,e){e===null?N(Nl,Nl.current):N(Nl,e.pool)}function kf(){var t=Qi();return t===null?null:{parent:Rt._currentValue,pool:t}}var kn=Error(s(460)),If=Error(s(474)),eu=Error(s(542)),Zi={then:function(){}};function Wf(t){return t=t.status,t==="fulfilled"||t==="rejected"}function lu(){}function $f(t,e,l){switch(l=t[l],l===void 0?t.push(e):l!==e&&(e.then(lu,lu),e=l),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,Pf(t),t;default:if(typeof e.status=="string")e.then(lu,lu);else{if(t=pt,t!==null&&100<t.shellSuspendCounter)throw Error(s(482));t=e,t.status="pending",t.then(function(n){if(e.status==="pending"){var a=e;a.status="fulfilled",a.value=n}},function(n){if(e.status==="pending"){var a=e;a.status="rejected",a.reason=n}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,Pf(t),t}throw In=e,kn}}var In=null;function Ff(){if(In===null)throw Error(s(459));var t=In;return In=null,t}function Pf(t){if(t===kn||t===eu)throw Error(s(483))}var Fe=!1;function Vi(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Ki(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function Pe(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function tl(t,e,l){var n=t.updateQueue;if(n===null)return null;if(n=n.shared,(rt&2)!==0){var a=n.pending;return a===null?e.next=e:(e.next=a.next,a.next=e),n.pending=e,e=ka(t),Gf(t,null,l),e}return Ja(t,n,e,l),ka(t)}function Wn(t,e,l){if(e=e.updateQueue,e!==null&&(e=e.shared,(l&4194048)!==0)){var n=e.lanes;n&=t.pendingLanes,l|=n,e.lanes=l,Vr(t,l)}}function Ji(t,e){var l=t.updateQueue,n=t.alternate;if(n!==null&&(n=n.updateQueue,l===n)){var a=null,u=null;if(l=l.firstBaseUpdate,l!==null){do{var f={lane:l.lane,tag:l.tag,payload:l.payload,callback:null,next:null};u===null?a=u=f:u=u.next=f,l=l.next}while(l!==null);u===null?a=u=e:u=u.next=e}else a=u=e;l={baseState:n.baseState,firstBaseUpdate:a,lastBaseUpdate:u,shared:n.shared,callbacks:n.callbacks},t.updateQueue=l;return}t=l.lastBaseUpdate,t===null?l.firstBaseUpdate=e:t.next=e,l.lastBaseUpdate=e}var ki=!1;function $n(){if(ki){var t=un;if(t!==null)throw t}}function Fn(t,e,l,n){ki=!1;var a=t.updateQueue;Fe=!1;var u=a.firstBaseUpdate,f=a.lastBaseUpdate,o=a.shared.pending;if(o!==null){a.shared.pending=null;var h=o,S=h.next;h.next=null,f===null?u=S:f.next=S,f=h;var O=t.alternate;O!==null&&(O=O.updateQueue,o=O.lastBaseUpdate,o!==f&&(o===null?O.firstBaseUpdate=S:o.next=S,O.lastBaseUpdate=h))}if(u!==null){var _=a.baseState;f=0,O=S=h=null,o=u;do{var T=o.lane&-536870913,D=T!==o.lane;if(D?(at&T)===T:(n&T)===T){T!==0&&T===an&&(ki=!0),O!==null&&(O=O.next={lane:0,tag:o.tag,payload:o.payload,callback:null,next:null});t:{var V=t,X=o;T=e;var ht=l;switch(X.tag){case 1:if(V=X.payload,typeof V=="function"){_=V.call(ht,_,T);break t}_=V;break t;case 3:V.flags=V.flags&-65537|128;case 0:if(V=X.payload,T=typeof V=="function"?V.call(ht,_,T):V,T==null)break t;_=z({},_,T);break t;case 2:Fe=!0}}T=o.callback,T!==null&&(t.flags|=64,D&&(t.flags|=8192),D=a.callbacks,D===null?a.callbacks=[T]:D.push(T))}else D={lane:T,tag:o.tag,payload:o.payload,callback:o.callback,next:null},O===null?(S=O=D,h=_):O=O.next=D,f|=T;if(o=o.next,o===null){if(o=a.shared.pending,o===null)break;D=o,o=D.next,D.next=null,a.lastBaseUpdate=D,a.shared.pending=null}}while(!0);O===null&&(h=_),a.baseState=h,a.firstBaseUpdate=S,a.lastBaseUpdate=O,u===null&&(a.shared.lanes=0),rl|=f,t.lanes=f,t.memoizedState=_}}function ts(t,e){if(typeof t!="function")throw Error(s(191,t));t.call(e)}function es(t,e){var l=t.callbacks;if(l!==null)for(t.callbacks=null,t=0;t<l.length;t++)ts(l[t],e)}var cn=x(null),nu=x(0);function ls(t,e){t=Ze,N(nu,t),N(cn,e),Ze=t|e.baseLanes}function Ii(){N(nu,Ze),N(cn,cn.current)}function Wi(){Ze=nu.current,q(cn),q(nu)}var el=0,W=null,ot=null,Ct=null,au=!1,rn=!1,Ul=!1,uu=0,Pn=0,fn=null,Bg=0;function Et(){throw Error(s(321))}function $i(t,e){if(e===null)return!1;for(var l=0;l<e.length&&l<t.length;l++)if(!ee(t[l],e[l]))return!1;return!0}function Fi(t,e,l,n,a,u){return el=u,W=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,E.H=t===null||t.memoizedState===null?Gs:ws,Ul=!1,u=l(n,a),Ul=!1,rn&&(u=as(e,l,n,a)),ns(t),u}function ns(t){E.H=ou;var e=ot!==null&&ot.next!==null;if(el=0,Ct=ot=W=null,au=!1,Pn=0,fn=null,e)throw Error(s(300));t===null||Nt||(t=t.dependencies,t!==null&&Fa(t)&&(Nt=!0))}function as(t,e,l,n){W=t;var a=0;do{if(rn&&(fn=null),Pn=0,rn=!1,25<=a)throw Error(s(301));if(a+=1,Ct=ot=null,t.updateQueue!=null){var u=t.updateQueue;u.lastEffect=null,u.events=null,u.stores=null,u.memoCache!=null&&(u.memoCache.index=0)}E.H=Zg,u=e(l,n)}while(rn);return u}function Yg(){var t=E.H,e=t.useState()[0];return e=typeof e.then=="function"?ta(e):e,t=t.useState()[0],(ot!==null?ot.memoizedState:null)!==t&&(W.flags|=1024),e}function Pi(){var t=uu!==0;return uu=0,t}function tc(t,e,l){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~l}function ec(t){if(au){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}au=!1}el=0,Ct=ot=W=null,rn=!1,Pn=uu=0,fn=null}function It(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ct===null?W.memoizedState=Ct=t:Ct=Ct.next=t,Ct}function _t(){if(ot===null){var t=W.alternate;t=t!==null?t.memoizedState:null}else t=ot.next;var e=Ct===null?W.memoizedState:Ct.next;if(e!==null)Ct=e,ot=t;else{if(t===null)throw W.alternate===null?Error(s(467)):Error(s(310));ot=t,t={memoizedState:ot.memoizedState,baseState:ot.baseState,baseQueue:ot.baseQueue,queue:ot.queue,next:null},Ct===null?W.memoizedState=Ct=t:Ct=Ct.next=t}return Ct}function lc(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function ta(t){var e=Pn;return Pn+=1,fn===null&&(fn=[]),t=$f(fn,t,e),e=W,(Ct===null?e.memoizedState:Ct.next)===null&&(e=e.alternate,E.H=e===null||e.memoizedState===null?Gs:ws),t}function iu(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return ta(t);if(t.$$typeof===mt)return Qt(t)}throw Error(s(438,String(t)))}function nc(t){var e=null,l=W.updateQueue;if(l!==null&&(e=l.memoCache),e==null){var n=W.alternate;n!==null&&(n=n.updateQueue,n!==null&&(n=n.memoCache,n!=null&&(e={data:n.data.map(function(a){return a.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),l===null&&(l=lc(),W.updateQueue=l),l.memoCache=e,l=e.data[e.index],l===void 0)for(l=e.data[e.index]=Array(t),n=0;n<t;n++)l[n]=Gl;return e.index++,l}function Ye(t,e){return typeof e=="function"?e(t):e}function cu(t){var e=_t();return ac(e,ot,t)}function ac(t,e,l){var n=t.queue;if(n===null)throw Error(s(311));n.lastRenderedReducer=l;var a=t.baseQueue,u=n.pending;if(u!==null){if(a!==null){var f=a.next;a.next=u.next,u.next=f}e.baseQueue=a=u,n.pending=null}if(u=t.baseState,a===null)t.memoizedState=u;else{e=a.next;var o=f=null,h=null,S=e,O=!1;do{var _=S.lane&-536870913;if(_!==S.lane?(at&_)===_:(el&_)===_){var T=S.revertLane;if(T===0)h!==null&&(h=h.next={lane:0,revertLane:0,action:S.action,hasEagerState:S.hasEagerState,eagerState:S.eagerState,next:null}),_===an&&(O=!0);else if((el&T)===T){S=S.next,T===an&&(O=!0);continue}else _={lane:0,revertLane:S.revertLane,action:S.action,hasEagerState:S.hasEagerState,eagerState:S.eagerState,next:null},h===null?(o=h=_,f=u):h=h.next=_,W.lanes|=T,rl|=T;_=S.action,Ul&&l(u,_),u=S.hasEagerState?S.eagerState:l(u,_)}else T={lane:_,revertLane:S.revertLane,action:S.action,hasEagerState:S.hasEagerState,eagerState:S.eagerState,next:null},h===null?(o=h=T,f=u):h=h.next=T,W.lanes|=_,rl|=_;S=S.next}while(S!==null&&S!==e);if(h===null?f=u:h.next=o,!ee(u,t.memoizedState)&&(Nt=!0,O&&(l=un,l!==null)))throw l;t.memoizedState=u,t.baseState=f,t.baseQueue=h,n.lastRenderedState=u}return a===null&&(n.lanes=0),[t.memoizedState,n.dispatch]}function uc(t){var e=_t(),l=e.queue;if(l===null)throw Error(s(311));l.lastRenderedReducer=t;var n=l.dispatch,a=l.pending,u=e.memoizedState;if(a!==null){l.pending=null;var f=a=a.next;do u=t(u,f.action),f=f.next;while(f!==a);ee(u,e.memoizedState)||(Nt=!0),e.memoizedState=u,e.baseQueue===null&&(e.baseState=u),l.lastRenderedState=u}return[u,n]}function us(t,e,l){var n=W,a=_t(),u=ct;if(u){if(l===void 0)throw Error(s(407));l=l()}else l=e();var f=!ee((ot||a).memoizedState,l);f&&(a.memoizedState=l,Nt=!0),a=a.queue;var o=rs.bind(null,n,a,t);if(ea(2048,8,o,[t]),a.getSnapshot!==e||f||Ct!==null&&Ct.memoizedState.tag&1){if(n.flags|=2048,sn(9,ru(),cs.bind(null,n,a,l,e),null),pt===null)throw Error(s(349));u||(el&124)!==0||is(n,e,l)}return l}function is(t,e,l){t.flags|=16384,t={getSnapshot:e,value:l},e=W.updateQueue,e===null?(e=lc(),W.updateQueue=e,e.stores=[t]):(l=e.stores,l===null?e.stores=[t]:l.push(t))}function cs(t,e,l,n){e.value=l,e.getSnapshot=n,fs(e)&&ss(t)}function rs(t,e,l){return l(function(){fs(e)&&ss(t)})}function fs(t){var e=t.getSnapshot;t=t.value;try{var l=e();return!ee(t,l)}catch{return!0}}function ss(t){var e=tn(t,2);e!==null&&ce(e,t,2)}function ic(t){var e=It();if(typeof t=="function"){var l=t;if(t=l(),Ul){ke(!0);try{l()}finally{ke(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ye,lastRenderedState:t},e}function os(t,e,l,n){return t.baseState=l,ac(t,ot,typeof n=="function"?n:Ye)}function Gg(t,e,l,n,a){if(su(t))throw Error(s(485));if(t=e.action,t!==null){var u={payload:a,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(f){u.listeners.push(f)}};E.T!==null?l(!0):u.isTransition=!1,n(u),l=e.pending,l===null?(u.next=e.pending=u,ds(e,u)):(u.next=l.next,e.pending=l.next=u)}}function ds(t,e){var l=e.action,n=e.payload,a=t.state;if(e.isTransition){var u=E.T,f={};E.T=f;try{var o=l(a,n),h=E.S;h!==null&&h(f,o),hs(t,e,o)}catch(S){cc(t,e,S)}finally{E.T=u}}else try{u=l(a,n),hs(t,e,u)}catch(S){cc(t,e,S)}}function hs(t,e,l){l!==null&&typeof l=="object"&&typeof l.then=="function"?l.then(function(n){gs(t,e,n)},function(n){return cc(t,e,n)}):gs(t,e,l)}function gs(t,e,l){e.status="fulfilled",e.value=l,vs(e),t.state=l,e=t.pending,e!==null&&(l=e.next,l===e?t.pending=null:(l=l.next,e.next=l,ds(t,l)))}function cc(t,e,l){var n=t.pending;if(t.pending=null,n!==null){n=n.next;do e.status="rejected",e.reason=l,vs(e),e=e.next;while(e!==n)}t.action=null}function vs(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function ys(t,e){return e}function ms(t,e){if(ct){var l=pt.formState;if(l!==null){t:{var n=W;if(ct){if(Dt){e:{for(var a=Dt,u=Ae;a.nodeType!==8;){if(!u){a=null;break e}if(a=De(a.nextSibling),a===null){a=null;break e}}u=a.data,a=u==="F!"||u==="F"?a:null}if(a){Dt=De(a.nextSibling),n=a.data==="F!";break t}}Rl(n)}n=!1}n&&(e=l[0])}}return l=It(),l.memoizedState=l.baseState=e,n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:ys,lastRenderedState:e},l.queue=n,l=js.bind(null,W,n),n.dispatch=l,n=ic(!1),u=dc.bind(null,W,!1,n.queue),n=It(),a={state:e,dispatch:null,action:t,pending:null},n.queue=a,l=Gg.bind(null,W,a,u,l),a.dispatch=l,n.memoizedState=t,[e,l,!1]}function ps(t){var e=_t();return Ss(e,ot,t)}function Ss(t,e,l){if(e=ac(t,e,ys)[0],t=cu(Ye)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var n=ta(e)}catch(f){throw f===kn?eu:f}else n=e;e=_t();var a=e.queue,u=a.dispatch;return l!==e.memoizedState&&(W.flags|=2048,sn(9,ru(),wg.bind(null,a,l),null)),[n,u,t]}function wg(t,e){t.action=e}function bs(t){var e=_t(),l=ot;if(l!==null)return Ss(e,l,t);_t(),e=e.memoizedState,l=_t();var n=l.queue.dispatch;return l.memoizedState=t,[e,n,!1]}function sn(t,e,l,n){return t={tag:t,create:l,deps:n,inst:e,next:null},e=W.updateQueue,e===null&&(e=lc(),W.updateQueue=e),l=e.lastEffect,l===null?e.lastEffect=t.next=t:(n=l.next,l.next=t,t.next=n,e.lastEffect=t),t}function ru(){return{destroy:void 0,resource:void 0}}function Ts(){return _t().memoizedState}function fu(t,e,l,n){var a=It();n=n===void 0?null:n,W.flags|=t,a.memoizedState=sn(1|e,ru(),l,n)}function ea(t,e,l,n){var a=_t();n=n===void 0?null:n;var u=a.memoizedState.inst;ot!==null&&n!==null&&$i(n,ot.memoizedState.deps)?a.memoizedState=sn(e,u,l,n):(W.flags|=t,a.memoizedState=sn(1|e,u,l,n))}function Ds(t,e){fu(8390656,8,t,e)}function Os(t,e){ea(2048,8,t,e)}function Es(t,e){return ea(4,2,t,e)}function As(t,e){return ea(4,4,t,e)}function Ms(t,e){if(typeof e=="function"){t=t();var l=e(t);return function(){typeof l=="function"?l():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function Cs(t,e,l){l=l!=null?l.concat([t]):null,ea(4,4,Ms.bind(null,e,t),l)}function rc(){}function _s(t,e){var l=_t();e=e===void 0?null:e;var n=l.memoizedState;return e!==null&&$i(e,n[1])?n[0]:(l.memoizedState=[t,e],t)}function Rs(t,e){var l=_t();e=e===void 0?null:e;var n=l.memoizedState;if(e!==null&&$i(e,n[1]))return n[0];if(n=t(),Ul){ke(!0);try{t()}finally{ke(!1)}}return l.memoizedState=[n,e],n}function fc(t,e,l){return l===void 0||(el&1073741824)!==0?t.memoizedState=e:(t.memoizedState=l,t=Uo(),W.lanes|=t,rl|=t,l)}function xs(t,e,l,n){return ee(l,e)?l:cn.current!==null?(t=fc(t,l,n),ee(t,e)||(Nt=!0),t):(el&42)===0?(Nt=!0,t.memoizedState=l):(t=Uo(),W.lanes|=t,rl|=t,e)}function zs(t,e,l,n,a){var u=U.p;U.p=u!==0&&8>u?u:8;var f=E.T,o={};E.T=o,dc(t,!1,e,l);try{var h=a(),S=E.S;if(S!==null&&S(o,h),h!==null&&typeof h=="object"&&typeof h.then=="function"){var O=jg(h,n);la(t,e,O,ie(t))}else la(t,e,n,ie(t))}catch(_){la(t,e,{then:function(){},status:"rejected",reason:_},ie())}finally{U.p=u,E.T=f}}function Xg(){}function sc(t,e,l,n){if(t.tag!==5)throw Error(s(476));var a=Ns(t).queue;zs(t,a,e,Z,l===null?Xg:function(){return Us(t),l(n)})}function Ns(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:Z,baseState:Z,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ye,lastRenderedState:Z},next:null};var l={};return e.next={memoizedState:l,baseState:l,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ye,lastRenderedState:l},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function Us(t){var e=Ns(t).next.queue;la(t,e,{},ie())}function oc(){return Qt(ba)}function Hs(){return _t().memoizedState}function qs(){return _t().memoizedState}function Lg(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var l=ie();t=Pe(l);var n=tl(e,t,l);n!==null&&(ce(n,e,l),Wn(n,e,l)),e={cache:Xi()},t.payload=e;return}e=e.return}}function Qg(t,e,l){var n=ie();l={lane:n,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null},su(t)?Bs(e,l):(l=zi(t,e,l,n),l!==null&&(ce(l,t,n),Ys(l,e,n)))}function js(t,e,l){var n=ie();la(t,e,l,n)}function la(t,e,l,n){var a={lane:n,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null};if(su(t))Bs(e,a);else{var u=t.alternate;if(t.lanes===0&&(u===null||u.lanes===0)&&(u=e.lastRenderedReducer,u!==null))try{var f=e.lastRenderedState,o=u(f,l);if(a.hasEagerState=!0,a.eagerState=o,ee(o,f))return Ja(t,e,a,0),pt===null&&Ka(),!1}catch{}finally{}if(l=zi(t,e,a,n),l!==null)return ce(l,t,n),Ys(l,e,n),!0}return!1}function dc(t,e,l,n){if(n={lane:2,revertLane:Zc(),action:n,hasEagerState:!1,eagerState:null,next:null},su(t)){if(e)throw Error(s(479))}else e=zi(t,l,n,2),e!==null&&ce(e,t,2)}function su(t){var e=t.alternate;return t===W||e!==null&&e===W}function Bs(t,e){rn=au=!0;var l=t.pending;l===null?e.next=e:(e.next=l.next,l.next=e),t.pending=e}function Ys(t,e,l){if((l&4194048)!==0){var n=e.lanes;n&=t.pendingLanes,l|=n,e.lanes=l,Vr(t,l)}}var ou={readContext:Qt,use:iu,useCallback:Et,useContext:Et,useEffect:Et,useImperativeHandle:Et,useLayoutEffect:Et,useInsertionEffect:Et,useMemo:Et,useReducer:Et,useRef:Et,useState:Et,useDebugValue:Et,useDeferredValue:Et,useTransition:Et,useSyncExternalStore:Et,useId:Et,useHostTransitionStatus:Et,useFormState:Et,useActionState:Et,useOptimistic:Et,useMemoCache:Et,useCacheRefresh:Et},Gs={readContext:Qt,use:iu,useCallback:function(t,e){return It().memoizedState=[t,e===void 0?null:e],t},useContext:Qt,useEffect:Ds,useImperativeHandle:function(t,e,l){l=l!=null?l.concat([t]):null,fu(4194308,4,Ms.bind(null,e,t),l)},useLayoutEffect:function(t,e){return fu(4194308,4,t,e)},useInsertionEffect:function(t,e){fu(4,2,t,e)},useMemo:function(t,e){var l=It();e=e===void 0?null:e;var n=t();if(Ul){ke(!0);try{t()}finally{ke(!1)}}return l.memoizedState=[n,e],n},useReducer:function(t,e,l){var n=It();if(l!==void 0){var a=l(e);if(Ul){ke(!0);try{l(e)}finally{ke(!1)}}}else a=e;return n.memoizedState=n.baseState=a,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:a},n.queue=t,t=t.dispatch=Qg.bind(null,W,t),[n.memoizedState,t]},useRef:function(t){var e=It();return t={current:t},e.memoizedState=t},useState:function(t){t=ic(t);var e=t.queue,l=js.bind(null,W,e);return e.dispatch=l,[t.memoizedState,l]},useDebugValue:rc,useDeferredValue:function(t,e){var l=It();return fc(l,t,e)},useTransition:function(){var t=ic(!1);return t=zs.bind(null,W,t.queue,!0,!1),It().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,l){var n=W,a=It();if(ct){if(l===void 0)throw Error(s(407));l=l()}else{if(l=e(),pt===null)throw Error(s(349));(at&124)!==0||is(n,e,l)}a.memoizedState=l;var u={value:l,getSnapshot:e};return a.queue=u,Ds(rs.bind(null,n,u,t),[t]),n.flags|=2048,sn(9,ru(),cs.bind(null,n,u,l,e),null),l},useId:function(){var t=It(),e=pt.identifierPrefix;if(ct){var l=qe,n=He;l=(n&~(1<<32-te(n)-1)).toString(32)+l,e="«"+e+"R"+l,l=uu++,0<l&&(e+="H"+l.toString(32)),e+="»"}else l=Bg++,e="«"+e+"r"+l.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:oc,useFormState:ms,useActionState:ms,useOptimistic:function(t){var e=It();e.memoizedState=e.baseState=t;var l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=l,e=dc.bind(null,W,!0,l),l.dispatch=e,[t,e]},useMemoCache:nc,useCacheRefresh:function(){return It().memoizedState=Lg.bind(null,W)}},ws={readContext:Qt,use:iu,useCallback:_s,useContext:Qt,useEffect:Os,useImperativeHandle:Cs,useInsertionEffect:Es,useLayoutEffect:As,useMemo:Rs,useReducer:cu,useRef:Ts,useState:function(){return cu(Ye)},useDebugValue:rc,useDeferredValue:function(t,e){var l=_t();return xs(l,ot.memoizedState,t,e)},useTransition:function(){var t=cu(Ye)[0],e=_t().memoizedState;return[typeof t=="boolean"?t:ta(t),e]},useSyncExternalStore:us,useId:Hs,useHostTransitionStatus:oc,useFormState:ps,useActionState:ps,useOptimistic:function(t,e){var l=_t();return os(l,ot,t,e)},useMemoCache:nc,useCacheRefresh:qs},Zg={readContext:Qt,use:iu,useCallback:_s,useContext:Qt,useEffect:Os,useImperativeHandle:Cs,useInsertionEffect:Es,useLayoutEffect:As,useMemo:Rs,useReducer:uc,useRef:Ts,useState:function(){return uc(Ye)},useDebugValue:rc,useDeferredValue:function(t,e){var l=_t();return ot===null?fc(l,t,e):xs(l,ot.memoizedState,t,e)},useTransition:function(){var t=uc(Ye)[0],e=_t().memoizedState;return[typeof t=="boolean"?t:ta(t),e]},useSyncExternalStore:us,useId:Hs,useHostTransitionStatus:oc,useFormState:bs,useActionState:bs,useOptimistic:function(t,e){var l=_t();return ot!==null?os(l,ot,t,e):(l.baseState=t,[t,l.queue.dispatch])},useMemoCache:nc,useCacheRefresh:qs},on=null,na=0;function du(t){var e=na;return na+=1,on===null&&(on=[]),$f(on,t,e)}function aa(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function hu(t,e){throw e.$$typeof===L?Error(s(525)):(t=Object.prototype.toString.call(e),Error(s(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function Xs(t){var e=t._init;return e(t._payload)}function Ls(t){function e(m,v){if(t){var p=m.deletions;p===null?(m.deletions=[v],m.flags|=16):p.push(v)}}function l(m,v){if(!t)return null;for(;v!==null;)e(m,v),v=v.sibling;return null}function n(m){for(var v=new Map;m!==null;)m.key!==null?v.set(m.key,m):v.set(m.index,m),m=m.sibling;return v}function a(m,v){return m=Ue(m,v),m.index=0,m.sibling=null,m}function u(m,v,p){return m.index=p,t?(p=m.alternate,p!==null?(p=p.index,p<v?(m.flags|=67108866,v):p):(m.flags|=67108866,v)):(m.flags|=1048576,v)}function f(m){return t&&m.alternate===null&&(m.flags|=67108866),m}function o(m,v,p,M){return v===null||v.tag!==6?(v=Ui(p,m.mode,M),v.return=m,v):(v=a(v,p),v.return=m,v)}function h(m,v,p,M){var j=p.type;return j===nt?O(m,v,p.props.children,M,p.key):v!==null&&(v.elementType===j||typeof j=="object"&&j!==null&&j.$$typeof===Vt&&Xs(j)===v.type)?(v=a(v,p.props),aa(v,p),v.return=m,v):(v=Ia(p.type,p.key,p.props,null,m.mode,M),aa(v,p),v.return=m,v)}function S(m,v,p,M){return v===null||v.tag!==4||v.stateNode.containerInfo!==p.containerInfo||v.stateNode.implementation!==p.implementation?(v=Hi(p,m.mode,M),v.return=m,v):(v=a(v,p.children||[]),v.return=m,v)}function O(m,v,p,M,j){return v===null||v.tag!==7?(v=Al(p,m.mode,M,j),v.return=m,v):(v=a(v,p),v.return=m,v)}function _(m,v,p){if(typeof v=="string"&&v!==""||typeof v=="number"||typeof v=="bigint")return v=Ui(""+v,m.mode,p),v.return=m,v;if(typeof v=="object"&&v!==null){switch(v.$$typeof){case G:return p=Ia(v.type,v.key,v.props,null,m.mode,p),aa(p,v),p.return=m,p;case B:return v=Hi(v,m.mode,p),v.return=m,v;case Vt:var M=v._init;return v=M(v._payload),_(m,v,p)}if(Xt(v)||wt(v))return v=Al(v,m.mode,p,null),v.return=m,v;if(typeof v.then=="function")return _(m,du(v),p);if(v.$$typeof===mt)return _(m,Pa(m,v),p);hu(m,v)}return null}function T(m,v,p,M){var j=v!==null?v.key:null;if(typeof p=="string"&&p!==""||typeof p=="number"||typeof p=="bigint")return j!==null?null:o(m,v,""+p,M);if(typeof p=="object"&&p!==null){switch(p.$$typeof){case G:return p.key===j?h(m,v,p,M):null;case B:return p.key===j?S(m,v,p,M):null;case Vt:return j=p._init,p=j(p._payload),T(m,v,p,M)}if(Xt(p)||wt(p))return j!==null?null:O(m,v,p,M,null);if(typeof p.then=="function")return T(m,v,du(p),M);if(p.$$typeof===mt)return T(m,v,Pa(m,p),M);hu(m,p)}return null}function D(m,v,p,M,j){if(typeof M=="string"&&M!==""||typeof M=="number"||typeof M=="bigint")return m=m.get(p)||null,o(v,m,""+M,j);if(typeof M=="object"&&M!==null){switch(M.$$typeof){case G:return m=m.get(M.key===null?p:M.key)||null,h(v,m,M,j);case B:return m=m.get(M.key===null?p:M.key)||null,S(v,m,M,j);case Vt:var F=M._init;return M=F(M._payload),D(m,v,p,M,j)}if(Xt(M)||wt(M))return m=m.get(p)||null,O(v,m,M,j,null);if(typeof M.then=="function")return D(m,v,p,du(M),j);if(M.$$typeof===mt)return D(m,v,p,Pa(v,M),j);hu(v,M)}return null}function V(m,v,p,M){for(var j=null,F=null,Y=v,Q=v=0,Ht=null;Y!==null&&Q<p.length;Q++){Y.index>Q?(Ht=Y,Y=null):Ht=Y.sibling;var it=T(m,Y,p[Q],M);if(it===null){Y===null&&(Y=Ht);break}t&&Y&&it.alternate===null&&e(m,Y),v=u(it,v,Q),F===null?j=it:F.sibling=it,F=it,Y=Ht}if(Q===p.length)return l(m,Y),ct&&Cl(m,Q),j;if(Y===null){for(;Q<p.length;Q++)Y=_(m,p[Q],M),Y!==null&&(v=u(Y,v,Q),F===null?j=Y:F.sibling=Y,F=Y);return ct&&Cl(m,Q),j}for(Y=n(Y);Q<p.length;Q++)Ht=D(Y,m,Q,p[Q],M),Ht!==null&&(t&&Ht.alternate!==null&&Y.delete(Ht.key===null?Q:Ht.key),v=u(Ht,v,Q),F===null?j=Ht:F.sibling=Ht,F=Ht);return t&&Y.forEach(function(ml){return e(m,ml)}),ct&&Cl(m,Q),j}function X(m,v,p,M){if(p==null)throw Error(s(151));for(var j=null,F=null,Y=v,Q=v=0,Ht=null,it=p.next();Y!==null&&!it.done;Q++,it=p.next()){Y.index>Q?(Ht=Y,Y=null):Ht=Y.sibling;var ml=T(m,Y,it.value,M);if(ml===null){Y===null&&(Y=Ht);break}t&&Y&&ml.alternate===null&&e(m,Y),v=u(ml,v,Q),F===null?j=ml:F.sibling=ml,F=ml,Y=Ht}if(it.done)return l(m,Y),ct&&Cl(m,Q),j;if(Y===null){for(;!it.done;Q++,it=p.next())it=_(m,it.value,M),it!==null&&(v=u(it,v,Q),F===null?j=it:F.sibling=it,F=it);return ct&&Cl(m,Q),j}for(Y=n(Y);!it.done;Q++,it=p.next())it=D(Y,m,Q,it.value,M),it!==null&&(t&&it.alternate!==null&&Y.delete(it.key===null?Q:it.key),v=u(it,v,Q),F===null?j=it:F.sibling=it,F=it);return t&&Y.forEach(function(Vv){return e(m,Vv)}),ct&&Cl(m,Q),j}function ht(m,v,p,M){if(typeof p=="object"&&p!==null&&p.type===nt&&p.key===null&&(p=p.props.children),typeof p=="object"&&p!==null){switch(p.$$typeof){case G:t:{for(var j=p.key;v!==null;){if(v.key===j){if(j=p.type,j===nt){if(v.tag===7){l(m,v.sibling),M=a(v,p.props.children),M.return=m,m=M;break t}}else if(v.elementType===j||typeof j=="object"&&j!==null&&j.$$typeof===Vt&&Xs(j)===v.type){l(m,v.sibling),M=a(v,p.props),aa(M,p),M.return=m,m=M;break t}l(m,v);break}else e(m,v);v=v.sibling}p.type===nt?(M=Al(p.props.children,m.mode,M,p.key),M.return=m,m=M):(M=Ia(p.type,p.key,p.props,null,m.mode,M),aa(M,p),M.return=m,m=M)}return f(m);case B:t:{for(j=p.key;v!==null;){if(v.key===j)if(v.tag===4&&v.stateNode.containerInfo===p.containerInfo&&v.stateNode.implementation===p.implementation){l(m,v.sibling),M=a(v,p.children||[]),M.return=m,m=M;break t}else{l(m,v);break}else e(m,v);v=v.sibling}M=Hi(p,m.mode,M),M.return=m,m=M}return f(m);case Vt:return j=p._init,p=j(p._payload),ht(m,v,p,M)}if(Xt(p))return V(m,v,p,M);if(wt(p)){if(j=wt(p),typeof j!="function")throw Error(s(150));return p=j.call(p),X(m,v,p,M)}if(typeof p.then=="function")return ht(m,v,du(p),M);if(p.$$typeof===mt)return ht(m,v,Pa(m,p),M);hu(m,p)}return typeof p=="string"&&p!==""||typeof p=="number"||typeof p=="bigint"?(p=""+p,v!==null&&v.tag===6?(l(m,v.sibling),M=a(v,p),M.return=m,m=M):(l(m,v),M=Ui(p,m.mode,M),M.return=m,m=M),f(m)):l(m,v)}return function(m,v,p,M){try{na=0;var j=ht(m,v,p,M);return on=null,j}catch(Y){if(Y===kn||Y===eu)throw Y;var F=le(29,Y,null,m.mode);return F.lanes=M,F.return=m,F}finally{}}}var dn=Ls(!0),Qs=Ls(!1),ge=x(null),Me=null;function ll(t){var e=t.alternate;N(xt,xt.current&1),N(ge,t),Me===null&&(e===null||cn.current!==null||e.memoizedState!==null)&&(Me=t)}function Zs(t){if(t.tag===22){if(N(xt,xt.current),N(ge,t),Me===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(Me=t)}}else nl()}function nl(){N(xt,xt.current),N(ge,ge.current)}function Ge(t){q(ge),Me===t&&(Me=null),q(xt)}var xt=x(0);function gu(t){for(var e=t;e!==null;){if(e.tag===13){var l=e.memoizedState;if(l!==null&&(l=l.dehydrated,l===null||l.data==="$?"||lr(l)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function hc(t,e,l,n){e=t.memoizedState,l=l(n,e),l=l==null?e:z({},e,l),t.memoizedState=l,t.lanes===0&&(t.updateQueue.baseState=l)}var gc={enqueueSetState:function(t,e,l){t=t._reactInternals;var n=ie(),a=Pe(n);a.payload=e,l!=null&&(a.callback=l),e=tl(t,a,n),e!==null&&(ce(e,t,n),Wn(e,t,n))},enqueueReplaceState:function(t,e,l){t=t._reactInternals;var n=ie(),a=Pe(n);a.tag=1,a.payload=e,l!=null&&(a.callback=l),e=tl(t,a,n),e!==null&&(ce(e,t,n),Wn(e,t,n))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var l=ie(),n=Pe(l);n.tag=2,e!=null&&(n.callback=e),e=tl(t,n,l),e!==null&&(ce(e,t,l),Wn(e,t,l))}};function Vs(t,e,l,n,a,u,f){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(n,u,f):e.prototype&&e.prototype.isPureReactComponent?!wn(l,n)||!wn(a,u):!0}function Ks(t,e,l,n){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(l,n),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(l,n),e.state!==t&&gc.enqueueReplaceState(e,e.state,null)}function Hl(t,e){var l=e;if("ref"in e){l={};for(var n in e)n!=="ref"&&(l[n]=e[n])}if(t=t.defaultProps){l===e&&(l=z({},l));for(var a in t)l[a]===void 0&&(l[a]=t[a])}return l}var vu=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function Js(t){vu(t)}function ks(t){console.error(t)}function Is(t){vu(t)}function yu(t,e){try{var l=t.onUncaughtError;l(e.value,{componentStack:e.stack})}catch(n){setTimeout(function(){throw n})}}function Ws(t,e,l){try{var n=t.onCaughtError;n(l.value,{componentStack:l.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(a){setTimeout(function(){throw a})}}function vc(t,e,l){return l=Pe(l),l.tag=3,l.payload={element:null},l.callback=function(){yu(t,e)},l}function $s(t){return t=Pe(t),t.tag=3,t}function Fs(t,e,l,n){var a=l.type.getDerivedStateFromError;if(typeof a=="function"){var u=n.value;t.payload=function(){return a(u)},t.callback=function(){Ws(e,l,n)}}var f=l.stateNode;f!==null&&typeof f.componentDidCatch=="function"&&(t.callback=function(){Ws(e,l,n),typeof a!="function"&&(fl===null?fl=new Set([this]):fl.add(this));var o=n.stack;this.componentDidCatch(n.value,{componentStack:o!==null?o:""})})}function Vg(t,e,l,n,a){if(l.flags|=32768,n!==null&&typeof n=="object"&&typeof n.then=="function"){if(e=l.alternate,e!==null&&Vn(e,l,a,!0),l=ge.current,l!==null){switch(l.tag){case 13:return Me===null?Gc():l.alternate===null&&Ot===0&&(Ot=3),l.flags&=-257,l.flags|=65536,l.lanes=a,n===Zi?l.flags|=16384:(e=l.updateQueue,e===null?l.updateQueue=new Set([n]):e.add(n),Xc(t,n,a)),!1;case 22:return l.flags|=65536,n===Zi?l.flags|=16384:(e=l.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([n])},l.updateQueue=e):(l=e.retryQueue,l===null?e.retryQueue=new Set([n]):l.add(n)),Xc(t,n,a)),!1}throw Error(s(435,l.tag))}return Xc(t,n,a),Gc(),!1}if(ct)return e=ge.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=a,n!==Bi&&(t=Error(s(422),{cause:n}),Zn(se(t,l)))):(n!==Bi&&(e=Error(s(423),{cause:n}),Zn(se(e,l))),t=t.current.alternate,t.flags|=65536,a&=-a,t.lanes|=a,n=se(n,l),a=vc(t.stateNode,n,a),Ji(t,a),Ot!==4&&(Ot=2)),!1;var u=Error(s(520),{cause:n});if(u=se(u,l),oa===null?oa=[u]:oa.push(u),Ot!==4&&(Ot=2),e===null)return!0;n=se(n,l),l=e;do{switch(l.tag){case 3:return l.flags|=65536,t=a&-a,l.lanes|=t,t=vc(l.stateNode,n,t),Ji(l,t),!1;case 1:if(e=l.type,u=l.stateNode,(l.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||u!==null&&typeof u.componentDidCatch=="function"&&(fl===null||!fl.has(u))))return l.flags|=65536,a&=-a,l.lanes|=a,a=$s(a),Fs(a,t,l,n),Ji(l,a),!1}l=l.return}while(l!==null);return!1}var Ps=Error(s(461)),Nt=!1;function qt(t,e,l,n){e.child=t===null?Qs(e,null,l,n):dn(e,t.child,l,n)}function to(t,e,l,n,a){l=l.render;var u=e.ref;if("ref"in n){var f={};for(var o in n)o!=="ref"&&(f[o]=n[o])}else f=n;return zl(e),n=Fi(t,e,l,f,u,a),o=Pi(),t!==null&&!Nt?(tc(t,e,a),we(t,e,a)):(ct&&o&&qi(e),e.flags|=1,qt(t,e,n,a),e.child)}function eo(t,e,l,n,a){if(t===null){var u=l.type;return typeof u=="function"&&!Ni(u)&&u.defaultProps===void 0&&l.compare===null?(e.tag=15,e.type=u,lo(t,e,u,n,a)):(t=Ia(l.type,null,n,e,e.mode,a),t.ref=e.ref,t.return=e,e.child=t)}if(u=t.child,!Oc(t,a)){var f=u.memoizedProps;if(l=l.compare,l=l!==null?l:wn,l(f,n)&&t.ref===e.ref)return we(t,e,a)}return e.flags|=1,t=Ue(u,n),t.ref=e.ref,t.return=e,e.child=t}function lo(t,e,l,n,a){if(t!==null){var u=t.memoizedProps;if(wn(u,n)&&t.ref===e.ref)if(Nt=!1,e.pendingProps=n=u,Oc(t,a))(t.flags&131072)!==0&&(Nt=!0);else return e.lanes=t.lanes,we(t,e,a)}return yc(t,e,l,n,a)}function no(t,e,l){var n=e.pendingProps,a=n.children,u=t!==null?t.memoizedState:null;if(n.mode==="hidden"){if((e.flags&128)!==0){if(n=u!==null?u.baseLanes|l:l,t!==null){for(a=e.child=t.child,u=0;a!==null;)u=u|a.lanes|a.childLanes,a=a.sibling;e.childLanes=u&~n}else e.childLanes=0,e.child=null;return ao(t,e,n,l)}if((l&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&tu(e,u!==null?u.cachePool:null),u!==null?ls(e,u):Ii(),Zs(e);else return e.lanes=e.childLanes=536870912,ao(t,e,u!==null?u.baseLanes|l:l,l)}else u!==null?(tu(e,u.cachePool),ls(e,u),nl(),e.memoizedState=null):(t!==null&&tu(e,null),Ii(),nl());return qt(t,e,a,l),e.child}function ao(t,e,l,n){var a=Qi();return a=a===null?null:{parent:Rt._currentValue,pool:a},e.memoizedState={baseLanes:l,cachePool:a},t!==null&&tu(e,null),Ii(),Zs(e),t!==null&&Vn(t,e,n,!0),null}function mu(t,e){var l=e.ref;if(l===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof l!="function"&&typeof l!="object")throw Error(s(284));(t===null||t.ref!==l)&&(e.flags|=4194816)}}function yc(t,e,l,n,a){return zl(e),l=Fi(t,e,l,n,void 0,a),n=Pi(),t!==null&&!Nt?(tc(t,e,a),we(t,e,a)):(ct&&n&&qi(e),e.flags|=1,qt(t,e,l,a),e.child)}function uo(t,e,l,n,a,u){return zl(e),e.updateQueue=null,l=as(e,n,l,a),ns(t),n=Pi(),t!==null&&!Nt?(tc(t,e,u),we(t,e,u)):(ct&&n&&qi(e),e.flags|=1,qt(t,e,l,u),e.child)}function io(t,e,l,n,a){if(zl(e),e.stateNode===null){var u=en,f=l.contextType;typeof f=="object"&&f!==null&&(u=Qt(f)),u=new l(n,u),e.memoizedState=u.state!==null&&u.state!==void 0?u.state:null,u.updater=gc,e.stateNode=u,u._reactInternals=e,u=e.stateNode,u.props=n,u.state=e.memoizedState,u.refs={},Vi(e),f=l.contextType,u.context=typeof f=="object"&&f!==null?Qt(f):en,u.state=e.memoizedState,f=l.getDerivedStateFromProps,typeof f=="function"&&(hc(e,l,f,n),u.state=e.memoizedState),typeof l.getDerivedStateFromProps=="function"||typeof u.getSnapshotBeforeUpdate=="function"||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(f=u.state,typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount(),f!==u.state&&gc.enqueueReplaceState(u,u.state,null),Fn(e,n,u,a),$n(),u.state=e.memoizedState),typeof u.componentDidMount=="function"&&(e.flags|=4194308),n=!0}else if(t===null){u=e.stateNode;var o=e.memoizedProps,h=Hl(l,o);u.props=h;var S=u.context,O=l.contextType;f=en,typeof O=="object"&&O!==null&&(f=Qt(O));var _=l.getDerivedStateFromProps;O=typeof _=="function"||typeof u.getSnapshotBeforeUpdate=="function",o=e.pendingProps!==o,O||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(o||S!==f)&&Ks(e,u,n,f),Fe=!1;var T=e.memoizedState;u.state=T,Fn(e,n,u,a),$n(),S=e.memoizedState,o||T!==S||Fe?(typeof _=="function"&&(hc(e,l,_,n),S=e.memoizedState),(h=Fe||Vs(e,l,h,n,T,S,f))?(O||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount()),typeof u.componentDidMount=="function"&&(e.flags|=4194308)):(typeof u.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=n,e.memoizedState=S),u.props=n,u.state=S,u.context=f,n=h):(typeof u.componentDidMount=="function"&&(e.flags|=4194308),n=!1)}else{u=e.stateNode,Ki(t,e),f=e.memoizedProps,O=Hl(l,f),u.props=O,_=e.pendingProps,T=u.context,S=l.contextType,h=en,typeof S=="object"&&S!==null&&(h=Qt(S)),o=l.getDerivedStateFromProps,(S=typeof o=="function"||typeof u.getSnapshotBeforeUpdate=="function")||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(f!==_||T!==h)&&Ks(e,u,n,h),Fe=!1,T=e.memoizedState,u.state=T,Fn(e,n,u,a),$n();var D=e.memoizedState;f!==_||T!==D||Fe||t!==null&&t.dependencies!==null&&Fa(t.dependencies)?(typeof o=="function"&&(hc(e,l,o,n),D=e.memoizedState),(O=Fe||Vs(e,l,O,n,T,D,h)||t!==null&&t.dependencies!==null&&Fa(t.dependencies))?(S||typeof u.UNSAFE_componentWillUpdate!="function"&&typeof u.componentWillUpdate!="function"||(typeof u.componentWillUpdate=="function"&&u.componentWillUpdate(n,D,h),typeof u.UNSAFE_componentWillUpdate=="function"&&u.UNSAFE_componentWillUpdate(n,D,h)),typeof u.componentDidUpdate=="function"&&(e.flags|=4),typeof u.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof u.componentDidUpdate!="function"||f===t.memoizedProps&&T===t.memoizedState||(e.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||f===t.memoizedProps&&T===t.memoizedState||(e.flags|=1024),e.memoizedProps=n,e.memoizedState=D),u.props=n,u.state=D,u.context=h,n=O):(typeof u.componentDidUpdate!="function"||f===t.memoizedProps&&T===t.memoizedState||(e.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||f===t.memoizedProps&&T===t.memoizedState||(e.flags|=1024),n=!1)}return u=n,mu(t,e),n=(e.flags&128)!==0,u||n?(u=e.stateNode,l=n&&typeof l.getDerivedStateFromError!="function"?null:u.render(),e.flags|=1,t!==null&&n?(e.child=dn(e,t.child,null,a),e.child=dn(e,null,l,a)):qt(t,e,l,a),e.memoizedState=u.state,t=e.child):t=we(t,e,a),t}function co(t,e,l,n){return Qn(),e.flags|=256,qt(t,e,l,n),e.child}var mc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function pc(t){return{baseLanes:t,cachePool:kf()}}function Sc(t,e,l){return t=t!==null?t.childLanes&~l:0,e&&(t|=ve),t}function ro(t,e,l){var n=e.pendingProps,a=!1,u=(e.flags&128)!==0,f;if((f=u)||(f=t!==null&&t.memoizedState===null?!1:(xt.current&2)!==0),f&&(a=!0,e.flags&=-129),f=(e.flags&32)!==0,e.flags&=-33,t===null){if(ct){if(a?ll(e):nl(),ct){var o=Dt,h;if(h=o){t:{for(h=o,o=Ae;h.nodeType!==8;){if(!o){o=null;break t}if(h=De(h.nextSibling),h===null){o=null;break t}}o=h}o!==null?(e.memoizedState={dehydrated:o,treeContext:Ml!==null?{id:He,overflow:qe}:null,retryLane:536870912,hydrationErrors:null},h=le(18,null,null,0),h.stateNode=o,h.return=e,e.child=h,Kt=e,Dt=null,h=!0):h=!1}h||Rl(e)}if(o=e.memoizedState,o!==null&&(o=o.dehydrated,o!==null))return lr(o)?e.lanes=32:e.lanes=536870912,null;Ge(e)}return o=n.children,n=n.fallback,a?(nl(),a=e.mode,o=pu({mode:"hidden",children:o},a),n=Al(n,a,l,null),o.return=e,n.return=e,o.sibling=n,e.child=o,a=e.child,a.memoizedState=pc(l),a.childLanes=Sc(t,f,l),e.memoizedState=mc,n):(ll(e),bc(e,o))}if(h=t.memoizedState,h!==null&&(o=h.dehydrated,o!==null)){if(u)e.flags&256?(ll(e),e.flags&=-257,e=Tc(t,e,l)):e.memoizedState!==null?(nl(),e.child=t.child,e.flags|=128,e=null):(nl(),a=n.fallback,o=e.mode,n=pu({mode:"visible",children:n.children},o),a=Al(a,o,l,null),a.flags|=2,n.return=e,a.return=e,n.sibling=a,e.child=n,dn(e,t.child,null,l),n=e.child,n.memoizedState=pc(l),n.childLanes=Sc(t,f,l),e.memoizedState=mc,e=a);else if(ll(e),lr(o)){if(f=o.nextSibling&&o.nextSibling.dataset,f)var S=f.dgst;f=S,n=Error(s(419)),n.stack="",n.digest=f,Zn({value:n,source:null,stack:null}),e=Tc(t,e,l)}else if(Nt||Vn(t,e,l,!1),f=(l&t.childLanes)!==0,Nt||f){if(f=pt,f!==null&&(n=l&-l,n=(n&42)!==0?1:ni(n),n=(n&(f.suspendedLanes|l))!==0?0:n,n!==0&&n!==h.retryLane))throw h.retryLane=n,tn(t,n),ce(f,t,n),Ps;o.data==="$?"||Gc(),e=Tc(t,e,l)}else o.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=h.treeContext,Dt=De(o.nextSibling),Kt=e,ct=!0,_l=null,Ae=!1,t!==null&&(de[he++]=He,de[he++]=qe,de[he++]=Ml,He=t.id,qe=t.overflow,Ml=e),e=bc(e,n.children),e.flags|=4096);return e}return a?(nl(),a=n.fallback,o=e.mode,h=t.child,S=h.sibling,n=Ue(h,{mode:"hidden",children:n.children}),n.subtreeFlags=h.subtreeFlags&65011712,S!==null?a=Ue(S,a):(a=Al(a,o,l,null),a.flags|=2),a.return=e,n.return=e,n.sibling=a,e.child=n,n=a,a=e.child,o=t.child.memoizedState,o===null?o=pc(l):(h=o.cachePool,h!==null?(S=Rt._currentValue,h=h.parent!==S?{parent:S,pool:S}:h):h=kf(),o={baseLanes:o.baseLanes|l,cachePool:h}),a.memoizedState=o,a.childLanes=Sc(t,f,l),e.memoizedState=mc,n):(ll(e),l=t.child,t=l.sibling,l=Ue(l,{mode:"visible",children:n.children}),l.return=e,l.sibling=null,t!==null&&(f=e.deletions,f===null?(e.deletions=[t],e.flags|=16):f.push(t)),e.child=l,e.memoizedState=null,l)}function bc(t,e){return e=pu({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function pu(t,e){return t=le(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function Tc(t,e,l){return dn(e,t.child,null,l),t=bc(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function fo(t,e,l){t.lanes|=e;var n=t.alternate;n!==null&&(n.lanes|=e),Gi(t.return,e,l)}function Dc(t,e,l,n,a){var u=t.memoizedState;u===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:n,tail:l,tailMode:a}:(u.isBackwards=e,u.rendering=null,u.renderingStartTime=0,u.last=n,u.tail=l,u.tailMode=a)}function so(t,e,l){var n=e.pendingProps,a=n.revealOrder,u=n.tail;if(qt(t,e,n.children,l),n=xt.current,(n&2)!==0)n=n&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&fo(t,l,e);else if(t.tag===19)fo(t,l,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}n&=1}switch(N(xt,n),a){case"forwards":for(l=e.child,a=null;l!==null;)t=l.alternate,t!==null&&gu(t)===null&&(a=l),l=l.sibling;l=a,l===null?(a=e.child,e.child=null):(a=l.sibling,l.sibling=null),Dc(e,!1,a,l,u);break;case"backwards":for(l=null,a=e.child,e.child=null;a!==null;){if(t=a.alternate,t!==null&&gu(t)===null){e.child=a;break}t=a.sibling,a.sibling=l,l=a,a=t}Dc(e,!0,l,null,u);break;case"together":Dc(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function we(t,e,l){if(t!==null&&(e.dependencies=t.dependencies),rl|=e.lanes,(l&e.childLanes)===0)if(t!==null){if(Vn(t,e,l,!1),(l&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(s(153));if(e.child!==null){for(t=e.child,l=Ue(t,t.pendingProps),e.child=l,l.return=e;t.sibling!==null;)t=t.sibling,l=l.sibling=Ue(t,t.pendingProps),l.return=e;l.sibling=null}return e.child}function Oc(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&Fa(t)))}function Kg(t,e,l){switch(e.tag){case 3:St(e,e.stateNode.containerInfo),$e(e,Rt,t.memoizedState.cache),Qn();break;case 27:case 5:Fu(e);break;case 4:St(e,e.stateNode.containerInfo);break;case 10:$e(e,e.type,e.memoizedProps.value);break;case 13:var n=e.memoizedState;if(n!==null)return n.dehydrated!==null?(ll(e),e.flags|=128,null):(l&e.child.childLanes)!==0?ro(t,e,l):(ll(e),t=we(t,e,l),t!==null?t.sibling:null);ll(e);break;case 19:var a=(t.flags&128)!==0;if(n=(l&e.childLanes)!==0,n||(Vn(t,e,l,!1),n=(l&e.childLanes)!==0),a){if(n)return so(t,e,l);e.flags|=128}if(a=e.memoizedState,a!==null&&(a.rendering=null,a.tail=null,a.lastEffect=null),N(xt,xt.current),n)break;return null;case 22:case 23:return e.lanes=0,no(t,e,l);case 24:$e(e,Rt,t.memoizedState.cache)}return we(t,e,l)}function oo(t,e,l){if(t!==null)if(t.memoizedProps!==e.pendingProps)Nt=!0;else{if(!Oc(t,l)&&(e.flags&128)===0)return Nt=!1,Kg(t,e,l);Nt=(t.flags&131072)!==0}else Nt=!1,ct&&(e.flags&1048576)!==0&&Xf(e,$a,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var n=e.elementType,a=n._init;if(n=a(n._payload),e.type=n,typeof n=="function")Ni(n)?(t=Hl(n,t),e.tag=1,e=io(null,e,n,t,l)):(e.tag=0,e=yc(null,e,n,t,l));else{if(n!=null){if(a=n.$$typeof,a===Yt){e.tag=11,e=to(null,e,n,t,l);break t}else if(a===Gt){e.tag=14,e=eo(null,e,n,t,l);break t}}throw e=Sl(n)||n,Error(s(306,e,""))}}return e;case 0:return yc(t,e,e.type,e.pendingProps,l);case 1:return n=e.type,a=Hl(n,e.pendingProps),io(t,e,n,a,l);case 3:t:{if(St(e,e.stateNode.containerInfo),t===null)throw Error(s(387));n=e.pendingProps;var u=e.memoizedState;a=u.element,Ki(t,e),Fn(e,n,null,l);var f=e.memoizedState;if(n=f.cache,$e(e,Rt,n),n!==u.cache&&wi(e,[Rt],l,!0),$n(),n=f.element,u.isDehydrated)if(u={element:n,isDehydrated:!1,cache:f.cache},e.updateQueue.baseState=u,e.memoizedState=u,e.flags&256){e=co(t,e,n,l);break t}else if(n!==a){a=se(Error(s(424)),e),Zn(a),e=co(t,e,n,l);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(Dt=De(t.firstChild),Kt=e,ct=!0,_l=null,Ae=!0,l=Qs(e,null,n,l),e.child=l;l;)l.flags=l.flags&-3|4096,l=l.sibling}else{if(Qn(),n===a){e=we(t,e,l);break t}qt(t,e,n,l)}e=e.child}return e;case 26:return mu(t,e),t===null?(l=yd(e.type,null,e.pendingProps,null))?e.memoizedState=l:ct||(l=e.type,t=e.pendingProps,n=Nu(K.current).createElement(l),n[Lt]=e,n[Jt]=t,Bt(n,l,t),zt(n),e.stateNode=n):e.memoizedState=yd(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return Fu(e),t===null&&ct&&(n=e.stateNode=hd(e.type,e.pendingProps,K.current),Kt=e,Ae=!0,a=Dt,dl(e.type)?(nr=a,Dt=De(n.firstChild)):Dt=a),qt(t,e,e.pendingProps.children,l),mu(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&ct&&((a=n=Dt)&&(n=bv(n,e.type,e.pendingProps,Ae),n!==null?(e.stateNode=n,Kt=e,Dt=De(n.firstChild),Ae=!1,a=!0):a=!1),a||Rl(e)),Fu(e),a=e.type,u=e.pendingProps,f=t!==null?t.memoizedProps:null,n=u.children,Pc(a,u)?n=null:f!==null&&Pc(a,f)&&(e.flags|=32),e.memoizedState!==null&&(a=Fi(t,e,Yg,null,null,l),ba._currentValue=a),mu(t,e),qt(t,e,n,l),e.child;case 6:return t===null&&ct&&((t=l=Dt)&&(l=Tv(l,e.pendingProps,Ae),l!==null?(e.stateNode=l,Kt=e,Dt=null,t=!0):t=!1),t||Rl(e)),null;case 13:return ro(t,e,l);case 4:return St(e,e.stateNode.containerInfo),n=e.pendingProps,t===null?e.child=dn(e,null,n,l):qt(t,e,n,l),e.child;case 11:return to(t,e,e.type,e.pendingProps,l);case 7:return qt(t,e,e.pendingProps,l),e.child;case 8:return qt(t,e,e.pendingProps.children,l),e.child;case 12:return qt(t,e,e.pendingProps.children,l),e.child;case 10:return n=e.pendingProps,$e(e,e.type,n.value),qt(t,e,n.children,l),e.child;case 9:return a=e.type._context,n=e.pendingProps.children,zl(e),a=Qt(a),n=n(a),e.flags|=1,qt(t,e,n,l),e.child;case 14:return eo(t,e,e.type,e.pendingProps,l);case 15:return lo(t,e,e.type,e.pendingProps,l);case 19:return so(t,e,l);case 31:return n=e.pendingProps,l=e.mode,n={mode:n.mode,children:n.children},t===null?(l=pu(n,l),l.ref=e.ref,e.child=l,l.return=e,e=l):(l=Ue(t.child,n),l.ref=e.ref,e.child=l,l.return=e,e=l),e;case 22:return no(t,e,l);case 24:return zl(e),n=Qt(Rt),t===null?(a=Qi(),a===null&&(a=pt,u=Xi(),a.pooledCache=u,u.refCount++,u!==null&&(a.pooledCacheLanes|=l),a=u),e.memoizedState={parent:n,cache:a},Vi(e),$e(e,Rt,a)):((t.lanes&l)!==0&&(Ki(t,e),Fn(e,null,null,l),$n()),a=t.memoizedState,u=e.memoizedState,a.parent!==n?(a={parent:n,cache:n},e.memoizedState=a,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=a),$e(e,Rt,n)):(n=u.cache,$e(e,Rt,n),n!==a.cache&&wi(e,[Rt],l,!0))),qt(t,e,e.pendingProps.children,l),e.child;case 29:throw e.pendingProps}throw Error(s(156,e.tag))}function Xe(t){t.flags|=4}function ho(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!Td(e)){if(e=ge.current,e!==null&&((at&4194048)===at?Me!==null:(at&62914560)!==at&&(at&536870912)===0||e!==Me))throw In=Zi,If;t.flags|=8192}}function Su(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?Qr():536870912,t.lanes|=e,yn|=e)}function ua(t,e){if(!ct)switch(t.tailMode){case"hidden":e=t.tail;for(var l=null;e!==null;)e.alternate!==null&&(l=e),e=e.sibling;l===null?t.tail=null:l.sibling=null;break;case"collapsed":l=t.tail;for(var n=null;l!==null;)l.alternate!==null&&(n=l),l=l.sibling;n===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:n.sibling=null}}function Tt(t){var e=t.alternate!==null&&t.alternate.child===t.child,l=0,n=0;if(e)for(var a=t.child;a!==null;)l|=a.lanes|a.childLanes,n|=a.subtreeFlags&65011712,n|=a.flags&65011712,a.return=t,a=a.sibling;else for(a=t.child;a!==null;)l|=a.lanes|a.childLanes,n|=a.subtreeFlags,n|=a.flags,a.return=t,a=a.sibling;return t.subtreeFlags|=n,t.childLanes=l,e}function Jg(t,e,l){var n=e.pendingProps;switch(ji(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Tt(e),null;case 1:return Tt(e),null;case 3:return l=e.stateNode,n=null,t!==null&&(n=t.memoizedState.cache),e.memoizedState.cache!==n&&(e.flags|=2048),Be(Rt),Je(),l.pendingContext&&(l.context=l.pendingContext,l.pendingContext=null),(t===null||t.child===null)&&(Ln(e)?Xe(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,Zf())),Tt(e),null;case 26:return l=e.memoizedState,t===null?(Xe(e),l!==null?(Tt(e),ho(e,l)):(Tt(e),e.flags&=-16777217)):l?l!==t.memoizedState?(Xe(e),Tt(e),ho(e,l)):(Tt(e),e.flags&=-16777217):(t.memoizedProps!==n&&Xe(e),Tt(e),e.flags&=-16777217),null;case 27:xa(e),l=K.current;var a=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==n&&Xe(e);else{if(!n){if(e.stateNode===null)throw Error(s(166));return Tt(e),null}t=w.current,Ln(e)?Lf(e):(t=hd(a,n,l),e.stateNode=t,Xe(e))}return Tt(e),null;case 5:if(xa(e),l=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==n&&Xe(e);else{if(!n){if(e.stateNode===null)throw Error(s(166));return Tt(e),null}if(t=w.current,Ln(e))Lf(e);else{switch(a=Nu(K.current),t){case 1:t=a.createElementNS("http://www.w3.org/2000/svg",l);break;case 2:t=a.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;default:switch(l){case"svg":t=a.createElementNS("http://www.w3.org/2000/svg",l);break;case"math":t=a.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;case"script":t=a.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof n.is=="string"?a.createElement("select",{is:n.is}):a.createElement("select"),n.multiple?t.multiple=!0:n.size&&(t.size=n.size);break;default:t=typeof n.is=="string"?a.createElement(l,{is:n.is}):a.createElement(l)}}t[Lt]=e,t[Jt]=n;t:for(a=e.child;a!==null;){if(a.tag===5||a.tag===6)t.appendChild(a.stateNode);else if(a.tag!==4&&a.tag!==27&&a.child!==null){a.child.return=a,a=a.child;continue}if(a===e)break t;for(;a.sibling===null;){if(a.return===null||a.return===e)break t;a=a.return}a.sibling.return=a.return,a=a.sibling}e.stateNode=t;t:switch(Bt(t,l,n),l){case"button":case"input":case"select":case"textarea":t=!!n.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&Xe(e)}}return Tt(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==n&&Xe(e);else{if(typeof n!="string"&&e.stateNode===null)throw Error(s(166));if(t=K.current,Ln(e)){if(t=e.stateNode,l=e.memoizedProps,n=null,a=Kt,a!==null)switch(a.tag){case 27:case 5:n=a.memoizedProps}t[Lt]=e,t=!!(t.nodeValue===l||n!==null&&n.suppressHydrationWarning===!0||id(t.nodeValue,l)),t||Rl(e)}else t=Nu(t).createTextNode(n),t[Lt]=e,e.stateNode=t}return Tt(e),null;case 13:if(n=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(a=Ln(e),n!==null&&n.dehydrated!==null){if(t===null){if(!a)throw Error(s(318));if(a=e.memoizedState,a=a!==null?a.dehydrated:null,!a)throw Error(s(317));a[Lt]=e}else Qn(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;Tt(e),a=!1}else a=Zf(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=a),a=!0;if(!a)return e.flags&256?(Ge(e),e):(Ge(e),null)}if(Ge(e),(e.flags&128)!==0)return e.lanes=l,e;if(l=n!==null,t=t!==null&&t.memoizedState!==null,l){n=e.child,a=null,n.alternate!==null&&n.alternate.memoizedState!==null&&n.alternate.memoizedState.cachePool!==null&&(a=n.alternate.memoizedState.cachePool.pool);var u=null;n.memoizedState!==null&&n.memoizedState.cachePool!==null&&(u=n.memoizedState.cachePool.pool),u!==a&&(n.flags|=2048)}return l!==t&&l&&(e.child.flags|=8192),Su(e,e.updateQueue),Tt(e),null;case 4:return Je(),t===null&&kc(e.stateNode.containerInfo),Tt(e),null;case 10:return Be(e.type),Tt(e),null;case 19:if(q(xt),a=e.memoizedState,a===null)return Tt(e),null;if(n=(e.flags&128)!==0,u=a.rendering,u===null)if(n)ua(a,!1);else{if(Ot!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(u=gu(t),u!==null){for(e.flags|=128,ua(a,!1),t=u.updateQueue,e.updateQueue=t,Su(e,t),e.subtreeFlags=0,t=l,l=e.child;l!==null;)wf(l,t),l=l.sibling;return N(xt,xt.current&1|2),e.child}t=t.sibling}a.tail!==null&&Ee()>Du&&(e.flags|=128,n=!0,ua(a,!1),e.lanes=4194304)}else{if(!n)if(t=gu(u),t!==null){if(e.flags|=128,n=!0,t=t.updateQueue,e.updateQueue=t,Su(e,t),ua(a,!0),a.tail===null&&a.tailMode==="hidden"&&!u.alternate&&!ct)return Tt(e),null}else 2*Ee()-a.renderingStartTime>Du&&l!==536870912&&(e.flags|=128,n=!0,ua(a,!1),e.lanes=4194304);a.isBackwards?(u.sibling=e.child,e.child=u):(t=a.last,t!==null?t.sibling=u:e.child=u,a.last=u)}return a.tail!==null?(e=a.tail,a.rendering=e,a.tail=e.sibling,a.renderingStartTime=Ee(),e.sibling=null,t=xt.current,N(xt,n?t&1|2:t&1),e):(Tt(e),null);case 22:case 23:return Ge(e),Wi(),n=e.memoizedState!==null,t!==null?t.memoizedState!==null!==n&&(e.flags|=8192):n&&(e.flags|=8192),n?(l&536870912)!==0&&(e.flags&128)===0&&(Tt(e),e.subtreeFlags&6&&(e.flags|=8192)):Tt(e),l=e.updateQueue,l!==null&&Su(e,l.retryQueue),l=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),n=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),n!==l&&(e.flags|=2048),t!==null&&q(Nl),null;case 24:return l=null,t!==null&&(l=t.memoizedState.cache),e.memoizedState.cache!==l&&(e.flags|=2048),Be(Rt),Tt(e),null;case 25:return null;case 30:return null}throw Error(s(156,e.tag))}function kg(t,e){switch(ji(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return Be(Rt),Je(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return xa(e),null;case 13:if(Ge(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(s(340));Qn()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return q(xt),null;case 4:return Je(),null;case 10:return Be(e.type),null;case 22:case 23:return Ge(e),Wi(),t!==null&&q(Nl),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return Be(Rt),null;case 25:return null;default:return null}}function go(t,e){switch(ji(e),e.tag){case 3:Be(Rt),Je();break;case 26:case 27:case 5:xa(e);break;case 4:Je();break;case 13:Ge(e);break;case 19:q(xt);break;case 10:Be(e.type);break;case 22:case 23:Ge(e),Wi(),t!==null&&q(Nl);break;case 24:Be(Rt)}}function ia(t,e){try{var l=e.updateQueue,n=l!==null?l.lastEffect:null;if(n!==null){var a=n.next;l=a;do{if((l.tag&t)===t){n=void 0;var u=l.create,f=l.inst;n=u(),f.destroy=n}l=l.next}while(l!==a)}}catch(o){vt(e,e.return,o)}}function al(t,e,l){try{var n=e.updateQueue,a=n!==null?n.lastEffect:null;if(a!==null){var u=a.next;n=u;do{if((n.tag&t)===t){var f=n.inst,o=f.destroy;if(o!==void 0){f.destroy=void 0,a=e;var h=l,S=o;try{S()}catch(O){vt(a,h,O)}}}n=n.next}while(n!==u)}}catch(O){vt(e,e.return,O)}}function vo(t){var e=t.updateQueue;if(e!==null){var l=t.stateNode;try{es(e,l)}catch(n){vt(t,t.return,n)}}}function yo(t,e,l){l.props=Hl(t.type,t.memoizedProps),l.state=t.memoizedState;try{l.componentWillUnmount()}catch(n){vt(t,e,n)}}function ca(t,e){try{var l=t.ref;if(l!==null){switch(t.tag){case 26:case 27:case 5:var n=t.stateNode;break;case 30:n=t.stateNode;break;default:n=t.stateNode}typeof l=="function"?t.refCleanup=l(n):l.current=n}}catch(a){vt(t,e,a)}}function Ce(t,e){var l=t.ref,n=t.refCleanup;if(l!==null)if(typeof n=="function")try{n()}catch(a){vt(t,e,a)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof l=="function")try{l(null)}catch(a){vt(t,e,a)}else l.current=null}function mo(t){var e=t.type,l=t.memoizedProps,n=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break t;case"img":l.src?n.src=l.src:l.srcSet&&(n.srcset=l.srcSet)}}catch(a){vt(t,t.return,a)}}function Ec(t,e,l){try{var n=t.stateNode;vv(n,t.type,l,e),n[Jt]=e}catch(a){vt(t,t.return,a)}}function po(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&dl(t.type)||t.tag===4}function Ac(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||po(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&dl(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function Mc(t,e,l){var n=t.tag;if(n===5||n===6)t=t.stateNode,e?(l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l).insertBefore(t,e):(e=l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l,e.appendChild(t),l=l._reactRootContainer,l!=null||e.onclick!==null||(e.onclick=zu));else if(n!==4&&(n===27&&dl(t.type)&&(l=t.stateNode,e=null),t=t.child,t!==null))for(Mc(t,e,l),t=t.sibling;t!==null;)Mc(t,e,l),t=t.sibling}function bu(t,e,l){var n=t.tag;if(n===5||n===6)t=t.stateNode,e?l.insertBefore(t,e):l.appendChild(t);else if(n!==4&&(n===27&&dl(t.type)&&(l=t.stateNode),t=t.child,t!==null))for(bu(t,e,l),t=t.sibling;t!==null;)bu(t,e,l),t=t.sibling}function So(t){var e=t.stateNode,l=t.memoizedProps;try{for(var n=t.type,a=e.attributes;a.length;)e.removeAttributeNode(a[0]);Bt(e,n,l),e[Lt]=t,e[Jt]=l}catch(u){vt(t,t.return,u)}}var Le=!1,At=!1,Cc=!1,bo=typeof WeakSet=="function"?WeakSet:Set,Ut=null;function Ig(t,e){if(t=t.containerInfo,$c=Yu,t=xf(t),Ai(t)){if("selectionStart"in t)var l={start:t.selectionStart,end:t.selectionEnd};else t:{l=(l=t.ownerDocument)&&l.defaultView||window;var n=l.getSelection&&l.getSelection();if(n&&n.rangeCount!==0){l=n.anchorNode;var a=n.anchorOffset,u=n.focusNode;n=n.focusOffset;try{l.nodeType,u.nodeType}catch{l=null;break t}var f=0,o=-1,h=-1,S=0,O=0,_=t,T=null;e:for(;;){for(var D;_!==l||a!==0&&_.nodeType!==3||(o=f+a),_!==u||n!==0&&_.nodeType!==3||(h=f+n),_.nodeType===3&&(f+=_.nodeValue.length),(D=_.firstChild)!==null;)T=_,_=D;for(;;){if(_===t)break e;if(T===l&&++S===a&&(o=f),T===u&&++O===n&&(h=f),(D=_.nextSibling)!==null)break;_=T,T=_.parentNode}_=D}l=o===-1||h===-1?null:{start:o,end:h}}else l=null}l=l||{start:0,end:0}}else l=null;for(Fc={focusedElem:t,selectionRange:l},Yu=!1,Ut=e;Ut!==null;)if(e=Ut,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,Ut=t;else for(;Ut!==null;){switch(e=Ut,u=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&u!==null){t=void 0,l=e,a=u.memoizedProps,u=u.memoizedState,n=l.stateNode;try{var V=Hl(l.type,a,l.elementType===l.type);t=n.getSnapshotBeforeUpdate(V,u),n.__reactInternalSnapshotBeforeUpdate=t}catch(X){vt(l,l.return,X)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,l=t.nodeType,l===9)er(t);else if(l===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":er(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(s(163))}if(t=e.sibling,t!==null){t.return=e.return,Ut=t;break}Ut=e.return}}function To(t,e,l){var n=l.flags;switch(l.tag){case 0:case 11:case 15:ul(t,l),n&4&&ia(5,l);break;case 1:if(ul(t,l),n&4)if(t=l.stateNode,e===null)try{t.componentDidMount()}catch(f){vt(l,l.return,f)}else{var a=Hl(l.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(a,e,t.__reactInternalSnapshotBeforeUpdate)}catch(f){vt(l,l.return,f)}}n&64&&vo(l),n&512&&ca(l,l.return);break;case 3:if(ul(t,l),n&64&&(t=l.updateQueue,t!==null)){if(e=null,l.child!==null)switch(l.child.tag){case 27:case 5:e=l.child.stateNode;break;case 1:e=l.child.stateNode}try{es(t,e)}catch(f){vt(l,l.return,f)}}break;case 27:e===null&&n&4&&So(l);case 26:case 5:ul(t,l),e===null&&n&4&&mo(l),n&512&&ca(l,l.return);break;case 12:ul(t,l);break;case 13:ul(t,l),n&4&&Eo(t,l),n&64&&(t=l.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(l=av.bind(null,l),Dv(t,l))));break;case 22:if(n=l.memoizedState!==null||Le,!n){e=e!==null&&e.memoizedState!==null||At,a=Le;var u=At;Le=n,(At=e)&&!u?il(t,l,(l.subtreeFlags&8772)!==0):ul(t,l),Le=a,At=u}break;case 30:break;default:ul(t,l)}}function Do(t){var e=t.alternate;e!==null&&(t.alternate=null,Do(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&ii(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var bt=null,Wt=!1;function Qe(t,e,l){for(l=l.child;l!==null;)Oo(t,e,l),l=l.sibling}function Oo(t,e,l){if(Pt&&typeof Pt.onCommitFiberUnmount=="function")try{Pt.onCommitFiberUnmount(_n,l)}catch{}switch(l.tag){case 26:At||Ce(l,e),Qe(t,e,l),l.memoizedState?l.memoizedState.count--:l.stateNode&&(l=l.stateNode,l.parentNode.removeChild(l));break;case 27:At||Ce(l,e);var n=bt,a=Wt;dl(l.type)&&(bt=l.stateNode,Wt=!1),Qe(t,e,l),ya(l.stateNode),bt=n,Wt=a;break;case 5:At||Ce(l,e);case 6:if(n=bt,a=Wt,bt=null,Qe(t,e,l),bt=n,Wt=a,bt!==null)if(Wt)try{(bt.nodeType===9?bt.body:bt.nodeName==="HTML"?bt.ownerDocument.body:bt).removeChild(l.stateNode)}catch(u){vt(l,e,u)}else try{bt.removeChild(l.stateNode)}catch(u){vt(l,e,u)}break;case 18:bt!==null&&(Wt?(t=bt,od(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,l.stateNode),Ea(t)):od(bt,l.stateNode));break;case 4:n=bt,a=Wt,bt=l.stateNode.containerInfo,Wt=!0,Qe(t,e,l),bt=n,Wt=a;break;case 0:case 11:case 14:case 15:At||al(2,l,e),At||al(4,l,e),Qe(t,e,l);break;case 1:At||(Ce(l,e),n=l.stateNode,typeof n.componentWillUnmount=="function"&&yo(l,e,n)),Qe(t,e,l);break;case 21:Qe(t,e,l);break;case 22:At=(n=At)||l.memoizedState!==null,Qe(t,e,l),At=n;break;default:Qe(t,e,l)}}function Eo(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{Ea(t)}catch(l){vt(e,e.return,l)}}function Wg(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new bo),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new bo),e;default:throw Error(s(435,t.tag))}}function _c(t,e){var l=Wg(t);e.forEach(function(n){var a=uv.bind(null,t,n);l.has(n)||(l.add(n),n.then(a,a))})}function ne(t,e){var l=e.deletions;if(l!==null)for(var n=0;n<l.length;n++){var a=l[n],u=t,f=e,o=f;t:for(;o!==null;){switch(o.tag){case 27:if(dl(o.type)){bt=o.stateNode,Wt=!1;break t}break;case 5:bt=o.stateNode,Wt=!1;break t;case 3:case 4:bt=o.stateNode.containerInfo,Wt=!0;break t}o=o.return}if(bt===null)throw Error(s(160));Oo(u,f,a),bt=null,Wt=!1,u=a.alternate,u!==null&&(u.return=null),a.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)Ao(e,t),e=e.sibling}var Te=null;function Ao(t,e){var l=t.alternate,n=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:ne(e,t),ae(t),n&4&&(al(3,t,t.return),ia(3,t),al(5,t,t.return));break;case 1:ne(e,t),ae(t),n&512&&(At||l===null||Ce(l,l.return)),n&64&&Le&&(t=t.updateQueue,t!==null&&(n=t.callbacks,n!==null&&(l=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=l===null?n:l.concat(n))));break;case 26:var a=Te;if(ne(e,t),ae(t),n&512&&(At||l===null||Ce(l,l.return)),n&4){var u=l!==null?l.memoizedState:null;if(n=t.memoizedState,l===null)if(n===null)if(t.stateNode===null){t:{n=t.type,l=t.memoizedProps,a=a.ownerDocument||a;e:switch(n){case"title":u=a.getElementsByTagName("title")[0],(!u||u[zn]||u[Lt]||u.namespaceURI==="http://www.w3.org/2000/svg"||u.hasAttribute("itemprop"))&&(u=a.createElement(n),a.head.insertBefore(u,a.querySelector("head > title"))),Bt(u,n,l),u[Lt]=t,zt(u),n=u;break t;case"link":var f=Sd("link","href",a).get(n+(l.href||""));if(f){for(var o=0;o<f.length;o++)if(u=f[o],u.getAttribute("href")===(l.href==null||l.href===""?null:l.href)&&u.getAttribute("rel")===(l.rel==null?null:l.rel)&&u.getAttribute("title")===(l.title==null?null:l.title)&&u.getAttribute("crossorigin")===(l.crossOrigin==null?null:l.crossOrigin)){f.splice(o,1);break e}}u=a.createElement(n),Bt(u,n,l),a.head.appendChild(u);break;case"meta":if(f=Sd("meta","content",a).get(n+(l.content||""))){for(o=0;o<f.length;o++)if(u=f[o],u.getAttribute("content")===(l.content==null?null:""+l.content)&&u.getAttribute("name")===(l.name==null?null:l.name)&&u.getAttribute("property")===(l.property==null?null:l.property)&&u.getAttribute("http-equiv")===(l.httpEquiv==null?null:l.httpEquiv)&&u.getAttribute("charset")===(l.charSet==null?null:l.charSet)){f.splice(o,1);break e}}u=a.createElement(n),Bt(u,n,l),a.head.appendChild(u);break;default:throw Error(s(468,n))}u[Lt]=t,zt(u),n=u}t.stateNode=n}else bd(a,t.type,t.stateNode);else t.stateNode=pd(a,n,t.memoizedProps);else u!==n?(u===null?l.stateNode!==null&&(l=l.stateNode,l.parentNode.removeChild(l)):u.count--,n===null?bd(a,t.type,t.stateNode):pd(a,n,t.memoizedProps)):n===null&&t.stateNode!==null&&Ec(t,t.memoizedProps,l.memoizedProps)}break;case 27:ne(e,t),ae(t),n&512&&(At||l===null||Ce(l,l.return)),l!==null&&n&4&&Ec(t,t.memoizedProps,l.memoizedProps);break;case 5:if(ne(e,t),ae(t),n&512&&(At||l===null||Ce(l,l.return)),t.flags&32){a=t.stateNode;try{Jl(a,"")}catch(D){vt(t,t.return,D)}}n&4&&t.stateNode!=null&&(a=t.memoizedProps,Ec(t,a,l!==null?l.memoizedProps:a)),n&1024&&(Cc=!0);break;case 6:if(ne(e,t),ae(t),n&4){if(t.stateNode===null)throw Error(s(162));n=t.memoizedProps,l=t.stateNode;try{l.nodeValue=n}catch(D){vt(t,t.return,D)}}break;case 3:if(qu=null,a=Te,Te=Uu(e.containerInfo),ne(e,t),Te=a,ae(t),n&4&&l!==null&&l.memoizedState.isDehydrated)try{Ea(e.containerInfo)}catch(D){vt(t,t.return,D)}Cc&&(Cc=!1,Mo(t));break;case 4:n=Te,Te=Uu(t.stateNode.containerInfo),ne(e,t),ae(t),Te=n;break;case 12:ne(e,t),ae(t);break;case 13:ne(e,t),ae(t),t.child.flags&8192&&t.memoizedState!==null!=(l!==null&&l.memoizedState!==null)&&(Hc=Ee()),n&4&&(n=t.updateQueue,n!==null&&(t.updateQueue=null,_c(t,n)));break;case 22:a=t.memoizedState!==null;var h=l!==null&&l.memoizedState!==null,S=Le,O=At;if(Le=S||a,At=O||h,ne(e,t),At=O,Le=S,ae(t),n&8192)t:for(e=t.stateNode,e._visibility=a?e._visibility&-2:e._visibility|1,a&&(l===null||h||Le||At||ql(t)),l=null,e=t;;){if(e.tag===5||e.tag===26){if(l===null){h=l=e;try{if(u=h.stateNode,a)f=u.style,typeof f.setProperty=="function"?f.setProperty("display","none","important"):f.display="none";else{o=h.stateNode;var _=h.memoizedProps.style,T=_!=null&&_.hasOwnProperty("display")?_.display:null;o.style.display=T==null||typeof T=="boolean"?"":(""+T).trim()}}catch(D){vt(h,h.return,D)}}}else if(e.tag===6){if(l===null){h=e;try{h.stateNode.nodeValue=a?"":h.memoizedProps}catch(D){vt(h,h.return,D)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;l===e&&(l=null),e=e.return}l===e&&(l=null),e.sibling.return=e.return,e=e.sibling}n&4&&(n=t.updateQueue,n!==null&&(l=n.retryQueue,l!==null&&(n.retryQueue=null,_c(t,l))));break;case 19:ne(e,t),ae(t),n&4&&(n=t.updateQueue,n!==null&&(t.updateQueue=null,_c(t,n)));break;case 30:break;case 21:break;default:ne(e,t),ae(t)}}function ae(t){var e=t.flags;if(e&2){try{for(var l,n=t.return;n!==null;){if(po(n)){l=n;break}n=n.return}if(l==null)throw Error(s(160));switch(l.tag){case 27:var a=l.stateNode,u=Ac(t);bu(t,u,a);break;case 5:var f=l.stateNode;l.flags&32&&(Jl(f,""),l.flags&=-33);var o=Ac(t);bu(t,o,f);break;case 3:case 4:var h=l.stateNode.containerInfo,S=Ac(t);Mc(t,S,h);break;default:throw Error(s(161))}}catch(O){vt(t,t.return,O)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function Mo(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;Mo(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function ul(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)To(t,e.alternate,e),e=e.sibling}function ql(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:al(4,e,e.return),ql(e);break;case 1:Ce(e,e.return);var l=e.stateNode;typeof l.componentWillUnmount=="function"&&yo(e,e.return,l),ql(e);break;case 27:ya(e.stateNode);case 26:case 5:Ce(e,e.return),ql(e);break;case 22:e.memoizedState===null&&ql(e);break;case 30:ql(e);break;default:ql(e)}t=t.sibling}}function il(t,e,l){for(l=l&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var n=e.alternate,a=t,u=e,f=u.flags;switch(u.tag){case 0:case 11:case 15:il(a,u,l),ia(4,u);break;case 1:if(il(a,u,l),n=u,a=n.stateNode,typeof a.componentDidMount=="function")try{a.componentDidMount()}catch(S){vt(n,n.return,S)}if(n=u,a=n.updateQueue,a!==null){var o=n.stateNode;try{var h=a.shared.hiddenCallbacks;if(h!==null)for(a.shared.hiddenCallbacks=null,a=0;a<h.length;a++)ts(h[a],o)}catch(S){vt(n,n.return,S)}}l&&f&64&&vo(u),ca(u,u.return);break;case 27:So(u);case 26:case 5:il(a,u,l),l&&n===null&&f&4&&mo(u),ca(u,u.return);break;case 12:il(a,u,l);break;case 13:il(a,u,l),l&&f&4&&Eo(a,u);break;case 22:u.memoizedState===null&&il(a,u,l),ca(u,u.return);break;case 30:break;default:il(a,u,l)}e=e.sibling}}function Rc(t,e){var l=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==l&&(t!=null&&t.refCount++,l!=null&&Kn(l))}function xc(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Kn(t))}function _e(t,e,l,n){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Co(t,e,l,n),e=e.sibling}function Co(t,e,l,n){var a=e.flags;switch(e.tag){case 0:case 11:case 15:_e(t,e,l,n),a&2048&&ia(9,e);break;case 1:_e(t,e,l,n);break;case 3:_e(t,e,l,n),a&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Kn(t)));break;case 12:if(a&2048){_e(t,e,l,n),t=e.stateNode;try{var u=e.memoizedProps,f=u.id,o=u.onPostCommit;typeof o=="function"&&o(f,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(h){vt(e,e.return,h)}}else _e(t,e,l,n);break;case 13:_e(t,e,l,n);break;case 23:break;case 22:u=e.stateNode,f=e.alternate,e.memoizedState!==null?u._visibility&2?_e(t,e,l,n):ra(t,e):u._visibility&2?_e(t,e,l,n):(u._visibility|=2,hn(t,e,l,n,(e.subtreeFlags&10256)!==0)),a&2048&&Rc(f,e);break;case 24:_e(t,e,l,n),a&2048&&xc(e.alternate,e);break;default:_e(t,e,l,n)}}function hn(t,e,l,n,a){for(a=a&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var u=t,f=e,o=l,h=n,S=f.flags;switch(f.tag){case 0:case 11:case 15:hn(u,f,o,h,a),ia(8,f);break;case 23:break;case 22:var O=f.stateNode;f.memoizedState!==null?O._visibility&2?hn(u,f,o,h,a):ra(u,f):(O._visibility|=2,hn(u,f,o,h,a)),a&&S&2048&&Rc(f.alternate,f);break;case 24:hn(u,f,o,h,a),a&&S&2048&&xc(f.alternate,f);break;default:hn(u,f,o,h,a)}e=e.sibling}}function ra(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var l=t,n=e,a=n.flags;switch(n.tag){case 22:ra(l,n),a&2048&&Rc(n.alternate,n);break;case 24:ra(l,n),a&2048&&xc(n.alternate,n);break;default:ra(l,n)}e=e.sibling}}var fa=8192;function gn(t){if(t.subtreeFlags&fa)for(t=t.child;t!==null;)_o(t),t=t.sibling}function _o(t){switch(t.tag){case 26:gn(t),t.flags&fa&&t.memoizedState!==null&&qv(Te,t.memoizedState,t.memoizedProps);break;case 5:gn(t);break;case 3:case 4:var e=Te;Te=Uu(t.stateNode.containerInfo),gn(t),Te=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=fa,fa=16777216,gn(t),fa=e):gn(t));break;default:gn(t)}}function Ro(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function sa(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var l=0;l<e.length;l++){var n=e[l];Ut=n,zo(n,t)}Ro(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)xo(t),t=t.sibling}function xo(t){switch(t.tag){case 0:case 11:case 15:sa(t),t.flags&2048&&al(9,t,t.return);break;case 3:sa(t);break;case 12:sa(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,Tu(t)):sa(t);break;default:sa(t)}}function Tu(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var l=0;l<e.length;l++){var n=e[l];Ut=n,zo(n,t)}Ro(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:al(8,e,e.return),Tu(e);break;case 22:l=e.stateNode,l._visibility&2&&(l._visibility&=-3,Tu(e));break;default:Tu(e)}t=t.sibling}}function zo(t,e){for(;Ut!==null;){var l=Ut;switch(l.tag){case 0:case 11:case 15:al(8,l,e);break;case 23:case 22:if(l.memoizedState!==null&&l.memoizedState.cachePool!==null){var n=l.memoizedState.cachePool.pool;n!=null&&n.refCount++}break;case 24:Kn(l.memoizedState.cache)}if(n=l.child,n!==null)n.return=l,Ut=n;else t:for(l=t;Ut!==null;){n=Ut;var a=n.sibling,u=n.return;if(Do(n),n===l){Ut=null;break t}if(a!==null){a.return=u,Ut=a;break t}Ut=u}}}var $g={getCacheForType:function(t){var e=Qt(Rt),l=e.data.get(t);return l===void 0&&(l=t(),e.data.set(t,l)),l}},Fg=typeof WeakMap=="function"?WeakMap:Map,rt=0,pt=null,tt=null,at=0,ft=0,ue=null,cl=!1,vn=!1,zc=!1,Ze=0,Ot=0,rl=0,jl=0,Nc=0,ve=0,yn=0,oa=null,$t=null,Uc=!1,Hc=0,Du=1/0,Ou=null,fl=null,jt=0,sl=null,mn=null,pn=0,qc=0,jc=null,No=null,da=0,Bc=null;function ie(){if((rt&2)!==0&&at!==0)return at&-at;if(E.T!==null){var t=an;return t!==0?t:Zc()}return Kr()}function Uo(){ve===0&&(ve=(at&536870912)===0||ct?Lr():536870912);var t=ge.current;return t!==null&&(t.flags|=32),ve}function ce(t,e,l){(t===pt&&(ft===2||ft===9)||t.cancelPendingCommit!==null)&&(Sn(t,0),ol(t,at,ve,!1)),xn(t,l),((rt&2)===0||t!==pt)&&(t===pt&&((rt&2)===0&&(jl|=l),Ot===4&&ol(t,at,ve,!1)),Re(t))}function Ho(t,e,l){if((rt&6)!==0)throw Error(s(327));var n=!l&&(e&124)===0&&(e&t.expiredLanes)===0||Rn(t,e),a=n?ev(t,e):wc(t,e,!0),u=n;do{if(a===0){vn&&!n&&ol(t,e,0,!1);break}else{if(l=t.current.alternate,u&&!Pg(l)){a=wc(t,e,!1),u=!1;continue}if(a===2){if(u=e,t.errorRecoveryDisabledLanes&u)var f=0;else f=t.pendingLanes&-536870913,f=f!==0?f:f&536870912?536870912:0;if(f!==0){e=f;t:{var o=t;a=oa;var h=o.current.memoizedState.isDehydrated;if(h&&(Sn(o,f).flags|=256),f=wc(o,f,!1),f!==2){if(zc&&!h){o.errorRecoveryDisabledLanes|=u,jl|=u,a=4;break t}u=$t,$t=a,u!==null&&($t===null?$t=u:$t.push.apply($t,u))}a=f}if(u=!1,a!==2)continue}}if(a===1){Sn(t,0),ol(t,e,0,!0);break}t:{switch(n=t,u=a,u){case 0:case 1:throw Error(s(345));case 4:if((e&4194048)!==e)break;case 6:ol(n,e,ve,!cl);break t;case 2:$t=null;break;case 3:case 5:break;default:throw Error(s(329))}if((e&62914560)===e&&(a=Hc+300-Ee(),10<a)){if(ol(n,e,ve,!cl),Ha(n,0,!0)!==0)break t;n.timeoutHandle=fd(qo.bind(null,n,l,$t,Ou,Uc,e,ve,jl,yn,cl,u,2,-0,0),a);break t}qo(n,l,$t,Ou,Uc,e,ve,jl,yn,cl,u,0,-0,0)}}break}while(!0);Re(t)}function qo(t,e,l,n,a,u,f,o,h,S,O,_,T,D){if(t.timeoutHandle=-1,_=e.subtreeFlags,(_&8192||(_&16785408)===16785408)&&(Sa={stylesheets:null,count:0,unsuspend:Hv},_o(e),_=jv(),_!==null)){t.cancelPendingCommit=_(Lo.bind(null,t,e,u,l,n,a,f,o,h,O,1,T,D)),ol(t,u,f,!S);return}Lo(t,e,u,l,n,a,f,o,h)}function Pg(t){for(var e=t;;){var l=e.tag;if((l===0||l===11||l===15)&&e.flags&16384&&(l=e.updateQueue,l!==null&&(l=l.stores,l!==null)))for(var n=0;n<l.length;n++){var a=l[n],u=a.getSnapshot;a=a.value;try{if(!ee(u(),a))return!1}catch{return!1}}if(l=e.child,e.subtreeFlags&16384&&l!==null)l.return=e,e=l;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function ol(t,e,l,n){e&=~Nc,e&=~jl,t.suspendedLanes|=e,t.pingedLanes&=~e,n&&(t.warmLanes|=e),n=t.expirationTimes;for(var a=e;0<a;){var u=31-te(a),f=1<<u;n[u]=-1,a&=~f}l!==0&&Zr(t,l,e)}function Eu(){return(rt&6)===0?(ha(0),!1):!0}function Yc(){if(tt!==null){if(ft===0)var t=tt.return;else t=tt,je=xl=null,ec(t),on=null,na=0,t=tt;for(;t!==null;)go(t.alternate,t),t=t.return;tt=null}}function Sn(t,e){var l=t.timeoutHandle;l!==-1&&(t.timeoutHandle=-1,mv(l)),l=t.cancelPendingCommit,l!==null&&(t.cancelPendingCommit=null,l()),Yc(),pt=t,tt=l=Ue(t.current,null),at=e,ft=0,ue=null,cl=!1,vn=Rn(t,e),zc=!1,yn=ve=Nc=jl=rl=Ot=0,$t=oa=null,Uc=!1,(e&8)!==0&&(e|=e&32);var n=t.entangledLanes;if(n!==0)for(t=t.entanglements,n&=e;0<n;){var a=31-te(n),u=1<<a;e|=t[a],n&=~u}return Ze=e,Ka(),l}function jo(t,e){W=null,E.H=ou,e===kn||e===eu?(e=Ff(),ft=3):e===If?(e=Ff(),ft=4):ft=e===Ps?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,ue=e,tt===null&&(Ot=1,yu(t,se(e,t.current)))}function Bo(){var t=E.H;return E.H=ou,t===null?ou:t}function Yo(){var t=E.A;return E.A=$g,t}function Gc(){Ot=4,cl||(at&4194048)!==at&&ge.current!==null||(vn=!0),(rl&134217727)===0&&(jl&134217727)===0||pt===null||ol(pt,at,ve,!1)}function wc(t,e,l){var n=rt;rt|=2;var a=Bo(),u=Yo();(pt!==t||at!==e)&&(Ou=null,Sn(t,e)),e=!1;var f=Ot;t:do try{if(ft!==0&&tt!==null){var o=tt,h=ue;switch(ft){case 8:Yc(),f=6;break t;case 3:case 2:case 9:case 6:ge.current===null&&(e=!0);var S=ft;if(ft=0,ue=null,bn(t,o,h,S),l&&vn){f=0;break t}break;default:S=ft,ft=0,ue=null,bn(t,o,h,S)}}tv(),f=Ot;break}catch(O){jo(t,O)}while(!0);return e&&t.shellSuspendCounter++,je=xl=null,rt=n,E.H=a,E.A=u,tt===null&&(pt=null,at=0,Ka()),f}function tv(){for(;tt!==null;)Go(tt)}function ev(t,e){var l=rt;rt|=2;var n=Bo(),a=Yo();pt!==t||at!==e?(Ou=null,Du=Ee()+500,Sn(t,e)):vn=Rn(t,e);t:do try{if(ft!==0&&tt!==null){e=tt;var u=ue;e:switch(ft){case 1:ft=0,ue=null,bn(t,e,u,1);break;case 2:case 9:if(Wf(u)){ft=0,ue=null,wo(e);break}e=function(){ft!==2&&ft!==9||pt!==t||(ft=7),Re(t)},u.then(e,e);break t;case 3:ft=7;break t;case 4:ft=5;break t;case 7:Wf(u)?(ft=0,ue=null,wo(e)):(ft=0,ue=null,bn(t,e,u,7));break;case 5:var f=null;switch(tt.tag){case 26:f=tt.memoizedState;case 5:case 27:var o=tt;if(!f||Td(f)){ft=0,ue=null;var h=o.sibling;if(h!==null)tt=h;else{var S=o.return;S!==null?(tt=S,Au(S)):tt=null}break e}}ft=0,ue=null,bn(t,e,u,5);break;case 6:ft=0,ue=null,bn(t,e,u,6);break;case 8:Yc(),Ot=6;break t;default:throw Error(s(462))}}lv();break}catch(O){jo(t,O)}while(!0);return je=xl=null,E.H=n,E.A=a,rt=l,tt!==null?0:(pt=null,at=0,Ka(),Ot)}function lv(){for(;tt!==null&&!Eh();)Go(tt)}function Go(t){var e=oo(t.alternate,t,Ze);t.memoizedProps=t.pendingProps,e===null?Au(t):tt=e}function wo(t){var e=t,l=e.alternate;switch(e.tag){case 15:case 0:e=uo(l,e,e.pendingProps,e.type,void 0,at);break;case 11:e=uo(l,e,e.pendingProps,e.type.render,e.ref,at);break;case 5:ec(e);default:go(l,e),e=tt=wf(e,Ze),e=oo(l,e,Ze)}t.memoizedProps=t.pendingProps,e===null?Au(t):tt=e}function bn(t,e,l,n){je=xl=null,ec(e),on=null,na=0;var a=e.return;try{if(Vg(t,a,e,l,at)){Ot=1,yu(t,se(l,t.current)),tt=null;return}}catch(u){if(a!==null)throw tt=a,u;Ot=1,yu(t,se(l,t.current)),tt=null;return}e.flags&32768?(ct||n===1?t=!0:vn||(at&536870912)!==0?t=!1:(cl=t=!0,(n===2||n===9||n===3||n===6)&&(n=ge.current,n!==null&&n.tag===13&&(n.flags|=16384))),Xo(e,t)):Au(e)}function Au(t){var e=t;do{if((e.flags&32768)!==0){Xo(e,cl);return}t=e.return;var l=Jg(e.alternate,e,Ze);if(l!==null){tt=l;return}if(e=e.sibling,e!==null){tt=e;return}tt=e=t}while(e!==null);Ot===0&&(Ot=5)}function Xo(t,e){do{var l=kg(t.alternate,t);if(l!==null){l.flags&=32767,tt=l;return}if(l=t.return,l!==null&&(l.flags|=32768,l.subtreeFlags=0,l.deletions=null),!e&&(t=t.sibling,t!==null)){tt=t;return}tt=t=l}while(t!==null);Ot=6,tt=null}function Lo(t,e,l,n,a,u,f,o,h){t.cancelPendingCommit=null;do Mu();while(jt!==0);if((rt&6)!==0)throw Error(s(327));if(e!==null){if(e===t.current)throw Error(s(177));if(u=e.lanes|e.childLanes,u|=xi,Hh(t,l,u,f,o,h),t===pt&&(tt=pt=null,at=0),mn=e,sl=t,pn=l,qc=u,jc=a,No=n,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,iv(za,function(){return Jo(),null})):(t.callbackNode=null,t.callbackPriority=0),n=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||n){n=E.T,E.T=null,a=U.p,U.p=2,f=rt,rt|=4;try{Ig(t,e,l)}finally{rt=f,U.p=a,E.T=n}}jt=1,Qo(),Zo(),Vo()}}function Qo(){if(jt===1){jt=0;var t=sl,e=mn,l=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||l){l=E.T,E.T=null;var n=U.p;U.p=2;var a=rt;rt|=4;try{Ao(e,t);var u=Fc,f=xf(t.containerInfo),o=u.focusedElem,h=u.selectionRange;if(f!==o&&o&&o.ownerDocument&&Rf(o.ownerDocument.documentElement,o)){if(h!==null&&Ai(o)){var S=h.start,O=h.end;if(O===void 0&&(O=S),"selectionStart"in o)o.selectionStart=S,o.selectionEnd=Math.min(O,o.value.length);else{var _=o.ownerDocument||document,T=_&&_.defaultView||window;if(T.getSelection){var D=T.getSelection(),V=o.textContent.length,X=Math.min(h.start,V),ht=h.end===void 0?X:Math.min(h.end,V);!D.extend&&X>ht&&(f=ht,ht=X,X=f);var m=_f(o,X),v=_f(o,ht);if(m&&v&&(D.rangeCount!==1||D.anchorNode!==m.node||D.anchorOffset!==m.offset||D.focusNode!==v.node||D.focusOffset!==v.offset)){var p=_.createRange();p.setStart(m.node,m.offset),D.removeAllRanges(),X>ht?(D.addRange(p),D.extend(v.node,v.offset)):(p.setEnd(v.node,v.offset),D.addRange(p))}}}}for(_=[],D=o;D=D.parentNode;)D.nodeType===1&&_.push({element:D,left:D.scrollLeft,top:D.scrollTop});for(typeof o.focus=="function"&&o.focus(),o=0;o<_.length;o++){var M=_[o];M.element.scrollLeft=M.left,M.element.scrollTop=M.top}}Yu=!!$c,Fc=$c=null}finally{rt=a,U.p=n,E.T=l}}t.current=e,jt=2}}function Zo(){if(jt===2){jt=0;var t=sl,e=mn,l=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||l){l=E.T,E.T=null;var n=U.p;U.p=2;var a=rt;rt|=4;try{To(t,e.alternate,e)}finally{rt=a,U.p=n,E.T=l}}jt=3}}function Vo(){if(jt===4||jt===3){jt=0,Ah();var t=sl,e=mn,l=pn,n=No;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?jt=5:(jt=0,mn=sl=null,Ko(t,t.pendingLanes));var a=t.pendingLanes;if(a===0&&(fl=null),ai(l),e=e.stateNode,Pt&&typeof Pt.onCommitFiberRoot=="function")try{Pt.onCommitFiberRoot(_n,e,void 0,(e.current.flags&128)===128)}catch{}if(n!==null){e=E.T,a=U.p,U.p=2,E.T=null;try{for(var u=t.onRecoverableError,f=0;f<n.length;f++){var o=n[f];u(o.value,{componentStack:o.stack})}}finally{E.T=e,U.p=a}}(pn&3)!==0&&Mu(),Re(t),a=t.pendingLanes,(l&4194090)!==0&&(a&42)!==0?t===Bc?da++:(da=0,Bc=t):da=0,ha(0)}}function Ko(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,Kn(e)))}function Mu(t){return Qo(),Zo(),Vo(),Jo()}function Jo(){if(jt!==5)return!1;var t=sl,e=qc;qc=0;var l=ai(pn),n=E.T,a=U.p;try{U.p=32>l?32:l,E.T=null,l=jc,jc=null;var u=sl,f=pn;if(jt=0,mn=sl=null,pn=0,(rt&6)!==0)throw Error(s(331));var o=rt;if(rt|=4,xo(u.current),Co(u,u.current,f,l),rt=o,ha(0,!1),Pt&&typeof Pt.onPostCommitFiberRoot=="function")try{Pt.onPostCommitFiberRoot(_n,u)}catch{}return!0}finally{U.p=a,E.T=n,Ko(t,e)}}function ko(t,e,l){e=se(l,e),e=vc(t.stateNode,e,2),t=tl(t,e,2),t!==null&&(xn(t,2),Re(t))}function vt(t,e,l){if(t.tag===3)ko(t,t,l);else for(;e!==null;){if(e.tag===3){ko(e,t,l);break}else if(e.tag===1){var n=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof n.componentDidCatch=="function"&&(fl===null||!fl.has(n))){t=se(l,t),l=$s(2),n=tl(e,l,2),n!==null&&(Fs(l,n,e,t),xn(n,2),Re(n));break}}e=e.return}}function Xc(t,e,l){var n=t.pingCache;if(n===null){n=t.pingCache=new Fg;var a=new Set;n.set(e,a)}else a=n.get(e),a===void 0&&(a=new Set,n.set(e,a));a.has(l)||(zc=!0,a.add(l),t=nv.bind(null,t,e,l),e.then(t,t))}function nv(t,e,l){var n=t.pingCache;n!==null&&n.delete(e),t.pingedLanes|=t.suspendedLanes&l,t.warmLanes&=~l,pt===t&&(at&l)===l&&(Ot===4||Ot===3&&(at&62914560)===at&&300>Ee()-Hc?(rt&2)===0&&Sn(t,0):Nc|=l,yn===at&&(yn=0)),Re(t)}function Io(t,e){e===0&&(e=Qr()),t=tn(t,e),t!==null&&(xn(t,e),Re(t))}function av(t){var e=t.memoizedState,l=0;e!==null&&(l=e.retryLane),Io(t,l)}function uv(t,e){var l=0;switch(t.tag){case 13:var n=t.stateNode,a=t.memoizedState;a!==null&&(l=a.retryLane);break;case 19:n=t.stateNode;break;case 22:n=t.stateNode._retryCache;break;default:throw Error(s(314))}n!==null&&n.delete(e),Io(t,l)}function iv(t,e){return ti(t,e)}var Cu=null,Tn=null,Lc=!1,_u=!1,Qc=!1,Bl=0;function Re(t){t!==Tn&&t.next===null&&(Tn===null?Cu=Tn=t:Tn=Tn.next=t),_u=!0,Lc||(Lc=!0,rv())}function ha(t,e){if(!Qc&&_u){Qc=!0;do for(var l=!1,n=Cu;n!==null;){if(t!==0){var a=n.pendingLanes;if(a===0)var u=0;else{var f=n.suspendedLanes,o=n.pingedLanes;u=(1<<31-te(42|t)+1)-1,u&=a&~(f&~o),u=u&201326741?u&201326741|1:u?u|2:0}u!==0&&(l=!0,Po(n,u))}else u=at,u=Ha(n,n===pt?u:0,n.cancelPendingCommit!==null||n.timeoutHandle!==-1),(u&3)===0||Rn(n,u)||(l=!0,Po(n,u));n=n.next}while(l);Qc=!1}}function cv(){Wo()}function Wo(){_u=Lc=!1;var t=0;Bl!==0&&(yv()&&(t=Bl),Bl=0);for(var e=Ee(),l=null,n=Cu;n!==null;){var a=n.next,u=$o(n,e);u===0?(n.next=null,l===null?Cu=a:l.next=a,a===null&&(Tn=l)):(l=n,(t!==0||(u&3)!==0)&&(_u=!0)),n=a}ha(t)}function $o(t,e){for(var l=t.suspendedLanes,n=t.pingedLanes,a=t.expirationTimes,u=t.pendingLanes&-62914561;0<u;){var f=31-te(u),o=1<<f,h=a[f];h===-1?((o&l)===0||(o&n)!==0)&&(a[f]=Uh(o,e)):h<=e&&(t.expiredLanes|=o),u&=~o}if(e=pt,l=at,l=Ha(t,t===e?l:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),n=t.callbackNode,l===0||t===e&&(ft===2||ft===9)||t.cancelPendingCommit!==null)return n!==null&&n!==null&&ei(n),t.callbackNode=null,t.callbackPriority=0;if((l&3)===0||Rn(t,l)){if(e=l&-l,e===t.callbackPriority)return e;switch(n!==null&&ei(n),ai(l)){case 2:case 8:l=wr;break;case 32:l=za;break;case 268435456:l=Xr;break;default:l=za}return n=Fo.bind(null,t),l=ti(l,n),t.callbackPriority=e,t.callbackNode=l,e}return n!==null&&n!==null&&ei(n),t.callbackPriority=2,t.callbackNode=null,2}function Fo(t,e){if(jt!==0&&jt!==5)return t.callbackNode=null,t.callbackPriority=0,null;var l=t.callbackNode;if(Mu()&&t.callbackNode!==l)return null;var n=at;return n=Ha(t,t===pt?n:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),n===0?null:(Ho(t,n,e),$o(t,Ee()),t.callbackNode!=null&&t.callbackNode===l?Fo.bind(null,t):null)}function Po(t,e){if(Mu())return null;Ho(t,e,!0)}function rv(){pv(function(){(rt&6)!==0?ti(Gr,cv):Wo()})}function Zc(){return Bl===0&&(Bl=Lr()),Bl}function td(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:Ga(""+t)}function ed(t,e){var l=e.ownerDocument.createElement("input");return l.name=e.name,l.value=e.value,t.id&&l.setAttribute("form",t.id),e.parentNode.insertBefore(l,e),t=new FormData(t),l.parentNode.removeChild(l),t}function fv(t,e,l,n,a){if(e==="submit"&&l&&l.stateNode===a){var u=td((a[Jt]||null).action),f=n.submitter;f&&(e=(e=f[Jt]||null)?td(e.formAction):f.getAttribute("formAction"),e!==null&&(u=e,f=null));var o=new Qa("action","action",null,n,a);t.push({event:o,listeners:[{instance:null,listener:function(){if(n.defaultPrevented){if(Bl!==0){var h=f?ed(a,f):new FormData(a);sc(l,{pending:!0,data:h,method:a.method,action:u},null,h)}}else typeof u=="function"&&(o.preventDefault(),h=f?ed(a,f):new FormData(a),sc(l,{pending:!0,data:h,method:a.method,action:u},u,h))},currentTarget:a}]})}}for(var Vc=0;Vc<Ri.length;Vc++){var Kc=Ri[Vc],sv=Kc.toLowerCase(),ov=Kc[0].toUpperCase()+Kc.slice(1);be(sv,"on"+ov)}be(Uf,"onAnimationEnd"),be(Hf,"onAnimationIteration"),be(qf,"onAnimationStart"),be("dblclick","onDoubleClick"),be("focusin","onFocus"),be("focusout","onBlur"),be(_g,"onTransitionRun"),be(Rg,"onTransitionStart"),be(xg,"onTransitionCancel"),be(jf,"onTransitionEnd"),Zl("onMouseEnter",["mouseout","mouseover"]),Zl("onMouseLeave",["mouseout","mouseover"]),Zl("onPointerEnter",["pointerout","pointerover"]),Zl("onPointerLeave",["pointerout","pointerover"]),Tl("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Tl("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Tl("onBeforeInput",["compositionend","keypress","textInput","paste"]),Tl("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Tl("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Tl("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var ga="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),dv=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(ga));function ld(t,e){e=(e&4)!==0;for(var l=0;l<t.length;l++){var n=t[l],a=n.event;n=n.listeners;t:{var u=void 0;if(e)for(var f=n.length-1;0<=f;f--){var o=n[f],h=o.instance,S=o.currentTarget;if(o=o.listener,h!==u&&a.isPropagationStopped())break t;u=o,a.currentTarget=S;try{u(a)}catch(O){vu(O)}a.currentTarget=null,u=h}else for(f=0;f<n.length;f++){if(o=n[f],h=o.instance,S=o.currentTarget,o=o.listener,h!==u&&a.isPropagationStopped())break t;u=o,a.currentTarget=S;try{u(a)}catch(O){vu(O)}a.currentTarget=null,u=h}}}}function et(t,e){var l=e[ui];l===void 0&&(l=e[ui]=new Set);var n=t+"__bubble";l.has(n)||(nd(e,t,2,!1),l.add(n))}function Jc(t,e,l){var n=0;e&&(n|=4),nd(l,t,n,e)}var Ru="_reactListening"+Math.random().toString(36).slice(2);function kc(t){if(!t[Ru]){t[Ru]=!0,kr.forEach(function(l){l!=="selectionchange"&&(dv.has(l)||Jc(l,!1,t),Jc(l,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[Ru]||(e[Ru]=!0,Jc("selectionchange",!1,e))}}function nd(t,e,l,n){switch(Cd(e)){case 2:var a=Gv;break;case 8:a=wv;break;default:a=rr}l=a.bind(null,e,l,t),a=void 0,!yi||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(a=!0),n?a!==void 0?t.addEventListener(e,l,{capture:!0,passive:a}):t.addEventListener(e,l,!0):a!==void 0?t.addEventListener(e,l,{passive:a}):t.addEventListener(e,l,!1)}function Ic(t,e,l,n,a){var u=n;if((e&1)===0&&(e&2)===0&&n!==null)t:for(;;){if(n===null)return;var f=n.tag;if(f===3||f===4){var o=n.stateNode.containerInfo;if(o===a)break;if(f===4)for(f=n.return;f!==null;){var h=f.tag;if((h===3||h===4)&&f.stateNode.containerInfo===a)return;f=f.return}for(;o!==null;){if(f=Xl(o),f===null)return;if(h=f.tag,h===5||h===6||h===26||h===27){n=u=f;continue t}o=o.parentNode}}n=n.return}ff(function(){var S=u,O=gi(l),_=[];t:{var T=Bf.get(t);if(T!==void 0){var D=Qa,V=t;switch(t){case"keypress":if(Xa(l)===0)break t;case"keydown":case"keyup":D=ig;break;case"focusin":V="focus",D=bi;break;case"focusout":V="blur",D=bi;break;case"beforeblur":case"afterblur":D=bi;break;case"click":if(l.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":D=df;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":D=kh;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":D=fg;break;case Uf:case Hf:case qf:D=$h;break;case jf:D=og;break;case"scroll":case"scrollend":D=Kh;break;case"wheel":D=hg;break;case"copy":case"cut":case"paste":D=Ph;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":D=gf;break;case"toggle":case"beforetoggle":D=vg}var X=(e&4)!==0,ht=!X&&(t==="scroll"||t==="scrollend"),m=X?T!==null?T+"Capture":null:T;X=[];for(var v=S,p;v!==null;){var M=v;if(p=M.stateNode,M=M.tag,M!==5&&M!==26&&M!==27||p===null||m===null||(M=Un(v,m),M!=null&&X.push(va(v,M,p))),ht)break;v=v.return}0<X.length&&(T=new D(T,V,null,l,O),_.push({event:T,listeners:X}))}}if((e&7)===0){t:{if(T=t==="mouseover"||t==="pointerover",D=t==="mouseout"||t==="pointerout",T&&l!==hi&&(V=l.relatedTarget||l.fromElement)&&(Xl(V)||V[wl]))break t;if((D||T)&&(T=O.window===O?O:(T=O.ownerDocument)?T.defaultView||T.parentWindow:window,D?(V=l.relatedTarget||l.toElement,D=S,V=V?Xl(V):null,V!==null&&(ht=y(V),X=V.tag,V!==ht||X!==5&&X!==27&&X!==6)&&(V=null)):(D=null,V=S),D!==V)){if(X=df,M="onMouseLeave",m="onMouseEnter",v="mouse",(t==="pointerout"||t==="pointerover")&&(X=gf,M="onPointerLeave",m="onPointerEnter",v="pointer"),ht=D==null?T:Nn(D),p=V==null?T:Nn(V),T=new X(M,v+"leave",D,l,O),T.target=ht,T.relatedTarget=p,M=null,Xl(O)===S&&(X=new X(m,v+"enter",V,l,O),X.target=p,X.relatedTarget=ht,M=X),ht=M,D&&V)e:{for(X=D,m=V,v=0,p=X;p;p=Dn(p))v++;for(p=0,M=m;M;M=Dn(M))p++;for(;0<v-p;)X=Dn(X),v--;for(;0<p-v;)m=Dn(m),p--;for(;v--;){if(X===m||m!==null&&X===m.alternate)break e;X=Dn(X),m=Dn(m)}X=null}else X=null;D!==null&&ad(_,T,D,X,!1),V!==null&&ht!==null&&ad(_,ht,V,X,!0)}}t:{if(T=S?Nn(S):window,D=T.nodeName&&T.nodeName.toLowerCase(),D==="select"||D==="input"&&T.type==="file")var j=Df;else if(bf(T))if(Of)j=Ag;else{j=Og;var F=Dg}else D=T.nodeName,!D||D.toLowerCase()!=="input"||T.type!=="checkbox"&&T.type!=="radio"?S&&di(S.elementType)&&(j=Df):j=Eg;if(j&&(j=j(t,S))){Tf(_,j,l,O);break t}F&&F(t,T,S),t==="focusout"&&S&&T.type==="number"&&S.memoizedProps.value!=null&&oi(T,"number",T.value)}switch(F=S?Nn(S):window,t){case"focusin":(bf(F)||F.contentEditable==="true")&&($l=F,Mi=S,Xn=null);break;case"focusout":Xn=Mi=$l=null;break;case"mousedown":Ci=!0;break;case"contextmenu":case"mouseup":case"dragend":Ci=!1,zf(_,l,O);break;case"selectionchange":if(Cg)break;case"keydown":case"keyup":zf(_,l,O)}var Y;if(Di)t:{switch(t){case"compositionstart":var Q="onCompositionStart";break t;case"compositionend":Q="onCompositionEnd";break t;case"compositionupdate":Q="onCompositionUpdate";break t}Q=void 0}else Wl?pf(t,l)&&(Q="onCompositionEnd"):t==="keydown"&&l.keyCode===229&&(Q="onCompositionStart");Q&&(vf&&l.locale!=="ko"&&(Wl||Q!=="onCompositionStart"?Q==="onCompositionEnd"&&Wl&&(Y=sf()):(We=O,mi="value"in We?We.value:We.textContent,Wl=!0)),F=xu(S,Q),0<F.length&&(Q=new hf(Q,t,null,l,O),_.push({event:Q,listeners:F}),Y?Q.data=Y:(Y=Sf(l),Y!==null&&(Q.data=Y)))),(Y=mg?pg(t,l):Sg(t,l))&&(Q=xu(S,"onBeforeInput"),0<Q.length&&(F=new hf("onBeforeInput","beforeinput",null,l,O),_.push({event:F,listeners:Q}),F.data=Y)),fv(_,t,S,l,O)}ld(_,e)})}function va(t,e,l){return{instance:t,listener:e,currentTarget:l}}function xu(t,e){for(var l=e+"Capture",n=[];t!==null;){var a=t,u=a.stateNode;if(a=a.tag,a!==5&&a!==26&&a!==27||u===null||(a=Un(t,l),a!=null&&n.unshift(va(t,a,u)),a=Un(t,e),a!=null&&n.push(va(t,a,u))),t.tag===3)return n;t=t.return}return[]}function Dn(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function ad(t,e,l,n,a){for(var u=e._reactName,f=[];l!==null&&l!==n;){var o=l,h=o.alternate,S=o.stateNode;if(o=o.tag,h!==null&&h===n)break;o!==5&&o!==26&&o!==27||S===null||(h=S,a?(S=Un(l,u),S!=null&&f.unshift(va(l,S,h))):a||(S=Un(l,u),S!=null&&f.push(va(l,S,h)))),l=l.return}f.length!==0&&t.push({event:e,listeners:f})}var hv=/\r\n?/g,gv=/\u0000|\uFFFD/g;function ud(t){return(typeof t=="string"?t:""+t).replace(hv,`
`).replace(gv,"")}function id(t,e){return e=ud(e),ud(t)===e}function zu(){}function dt(t,e,l,n,a,u){switch(l){case"children":typeof n=="string"?e==="body"||e==="textarea"&&n===""||Jl(t,n):(typeof n=="number"||typeof n=="bigint")&&e!=="body"&&Jl(t,""+n);break;case"className":ja(t,"class",n);break;case"tabIndex":ja(t,"tabindex",n);break;case"dir":case"role":case"viewBox":case"width":case"height":ja(t,l,n);break;case"style":cf(t,n,u);break;case"data":if(e!=="object"){ja(t,"data",n);break}case"src":case"href":if(n===""&&(e!=="a"||l!=="href")){t.removeAttribute(l);break}if(n==null||typeof n=="function"||typeof n=="symbol"||typeof n=="boolean"){t.removeAttribute(l);break}n=Ga(""+n),t.setAttribute(l,n);break;case"action":case"formAction":if(typeof n=="function"){t.setAttribute(l,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof u=="function"&&(l==="formAction"?(e!=="input"&&dt(t,e,"name",a.name,a,null),dt(t,e,"formEncType",a.formEncType,a,null),dt(t,e,"formMethod",a.formMethod,a,null),dt(t,e,"formTarget",a.formTarget,a,null)):(dt(t,e,"encType",a.encType,a,null),dt(t,e,"method",a.method,a,null),dt(t,e,"target",a.target,a,null)));if(n==null||typeof n=="symbol"||typeof n=="boolean"){t.removeAttribute(l);break}n=Ga(""+n),t.setAttribute(l,n);break;case"onClick":n!=null&&(t.onclick=zu);break;case"onScroll":n!=null&&et("scroll",t);break;case"onScrollEnd":n!=null&&et("scrollend",t);break;case"dangerouslySetInnerHTML":if(n!=null){if(typeof n!="object"||!("__html"in n))throw Error(s(61));if(l=n.__html,l!=null){if(a.children!=null)throw Error(s(60));t.innerHTML=l}}break;case"multiple":t.multiple=n&&typeof n!="function"&&typeof n!="symbol";break;case"muted":t.muted=n&&typeof n!="function"&&typeof n!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(n==null||typeof n=="function"||typeof n=="boolean"||typeof n=="symbol"){t.removeAttribute("xlink:href");break}l=Ga(""+n),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",l);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":n!=null&&typeof n!="function"&&typeof n!="symbol"?t.setAttribute(l,""+n):t.removeAttribute(l);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":n&&typeof n!="function"&&typeof n!="symbol"?t.setAttribute(l,""):t.removeAttribute(l);break;case"capture":case"download":n===!0?t.setAttribute(l,""):n!==!1&&n!=null&&typeof n!="function"&&typeof n!="symbol"?t.setAttribute(l,n):t.removeAttribute(l);break;case"cols":case"rows":case"size":case"span":n!=null&&typeof n!="function"&&typeof n!="symbol"&&!isNaN(n)&&1<=n?t.setAttribute(l,n):t.removeAttribute(l);break;case"rowSpan":case"start":n==null||typeof n=="function"||typeof n=="symbol"||isNaN(n)?t.removeAttribute(l):t.setAttribute(l,n);break;case"popover":et("beforetoggle",t),et("toggle",t),qa(t,"popover",n);break;case"xlinkActuate":ze(t,"http://www.w3.org/1999/xlink","xlink:actuate",n);break;case"xlinkArcrole":ze(t,"http://www.w3.org/1999/xlink","xlink:arcrole",n);break;case"xlinkRole":ze(t,"http://www.w3.org/1999/xlink","xlink:role",n);break;case"xlinkShow":ze(t,"http://www.w3.org/1999/xlink","xlink:show",n);break;case"xlinkTitle":ze(t,"http://www.w3.org/1999/xlink","xlink:title",n);break;case"xlinkType":ze(t,"http://www.w3.org/1999/xlink","xlink:type",n);break;case"xmlBase":ze(t,"http://www.w3.org/XML/1998/namespace","xml:base",n);break;case"xmlLang":ze(t,"http://www.w3.org/XML/1998/namespace","xml:lang",n);break;case"xmlSpace":ze(t,"http://www.w3.org/XML/1998/namespace","xml:space",n);break;case"is":qa(t,"is",n);break;case"innerText":case"textContent":break;default:(!(2<l.length)||l[0]!=="o"&&l[0]!=="O"||l[1]!=="n"&&l[1]!=="N")&&(l=Zh.get(l)||l,qa(t,l,n))}}function Wc(t,e,l,n,a,u){switch(l){case"style":cf(t,n,u);break;case"dangerouslySetInnerHTML":if(n!=null){if(typeof n!="object"||!("__html"in n))throw Error(s(61));if(l=n.__html,l!=null){if(a.children!=null)throw Error(s(60));t.innerHTML=l}}break;case"children":typeof n=="string"?Jl(t,n):(typeof n=="number"||typeof n=="bigint")&&Jl(t,""+n);break;case"onScroll":n!=null&&et("scroll",t);break;case"onScrollEnd":n!=null&&et("scrollend",t);break;case"onClick":n!=null&&(t.onclick=zu);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Ir.hasOwnProperty(l))t:{if(l[0]==="o"&&l[1]==="n"&&(a=l.endsWith("Capture"),e=l.slice(2,a?l.length-7:void 0),u=t[Jt]||null,u=u!=null?u[l]:null,typeof u=="function"&&t.removeEventListener(e,u,a),typeof n=="function")){typeof u!="function"&&u!==null&&(l in t?t[l]=null:t.hasAttribute(l)&&t.removeAttribute(l)),t.addEventListener(e,n,a);break t}l in t?t[l]=n:n===!0?t.setAttribute(l,""):qa(t,l,n)}}}function Bt(t,e,l){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":et("error",t),et("load",t);var n=!1,a=!1,u;for(u in l)if(l.hasOwnProperty(u)){var f=l[u];if(f!=null)switch(u){case"src":n=!0;break;case"srcSet":a=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(s(137,e));default:dt(t,e,u,f,l,null)}}a&&dt(t,e,"srcSet",l.srcSet,l,null),n&&dt(t,e,"src",l.src,l,null);return;case"input":et("invalid",t);var o=u=f=a=null,h=null,S=null;for(n in l)if(l.hasOwnProperty(n)){var O=l[n];if(O!=null)switch(n){case"name":a=O;break;case"type":f=O;break;case"checked":h=O;break;case"defaultChecked":S=O;break;case"value":u=O;break;case"defaultValue":o=O;break;case"children":case"dangerouslySetInnerHTML":if(O!=null)throw Error(s(137,e));break;default:dt(t,e,n,O,l,null)}}lf(t,u,o,h,S,f,a,!1),Ba(t);return;case"select":et("invalid",t),n=f=u=null;for(a in l)if(l.hasOwnProperty(a)&&(o=l[a],o!=null))switch(a){case"value":u=o;break;case"defaultValue":f=o;break;case"multiple":n=o;default:dt(t,e,a,o,l,null)}e=u,l=f,t.multiple=!!n,e!=null?Kl(t,!!n,e,!1):l!=null&&Kl(t,!!n,l,!0);return;case"textarea":et("invalid",t),u=a=n=null;for(f in l)if(l.hasOwnProperty(f)&&(o=l[f],o!=null))switch(f){case"value":n=o;break;case"defaultValue":a=o;break;case"children":u=o;break;case"dangerouslySetInnerHTML":if(o!=null)throw Error(s(91));break;default:dt(t,e,f,o,l,null)}af(t,n,a,u),Ba(t);return;case"option":for(h in l)if(l.hasOwnProperty(h)&&(n=l[h],n!=null))switch(h){case"selected":t.selected=n&&typeof n!="function"&&typeof n!="symbol";break;default:dt(t,e,h,n,l,null)}return;case"dialog":et("beforetoggle",t),et("toggle",t),et("cancel",t),et("close",t);break;case"iframe":case"object":et("load",t);break;case"video":case"audio":for(n=0;n<ga.length;n++)et(ga[n],t);break;case"image":et("error",t),et("load",t);break;case"details":et("toggle",t);break;case"embed":case"source":case"link":et("error",t),et("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(S in l)if(l.hasOwnProperty(S)&&(n=l[S],n!=null))switch(S){case"children":case"dangerouslySetInnerHTML":throw Error(s(137,e));default:dt(t,e,S,n,l,null)}return;default:if(di(e)){for(O in l)l.hasOwnProperty(O)&&(n=l[O],n!==void 0&&Wc(t,e,O,n,l,void 0));return}}for(o in l)l.hasOwnProperty(o)&&(n=l[o],n!=null&&dt(t,e,o,n,l,null))}function vv(t,e,l,n){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var a=null,u=null,f=null,o=null,h=null,S=null,O=null;for(D in l){var _=l[D];if(l.hasOwnProperty(D)&&_!=null)switch(D){case"checked":break;case"value":break;case"defaultValue":h=_;default:n.hasOwnProperty(D)||dt(t,e,D,null,n,_)}}for(var T in n){var D=n[T];if(_=l[T],n.hasOwnProperty(T)&&(D!=null||_!=null))switch(T){case"type":u=D;break;case"name":a=D;break;case"checked":S=D;break;case"defaultChecked":O=D;break;case"value":f=D;break;case"defaultValue":o=D;break;case"children":case"dangerouslySetInnerHTML":if(D!=null)throw Error(s(137,e));break;default:D!==_&&dt(t,e,T,D,n,_)}}si(t,f,o,h,S,O,u,a);return;case"select":D=f=o=T=null;for(u in l)if(h=l[u],l.hasOwnProperty(u)&&h!=null)switch(u){case"value":break;case"multiple":D=h;default:n.hasOwnProperty(u)||dt(t,e,u,null,n,h)}for(a in n)if(u=n[a],h=l[a],n.hasOwnProperty(a)&&(u!=null||h!=null))switch(a){case"value":T=u;break;case"defaultValue":o=u;break;case"multiple":f=u;default:u!==h&&dt(t,e,a,u,n,h)}e=o,l=f,n=D,T!=null?Kl(t,!!l,T,!1):!!n!=!!l&&(e!=null?Kl(t,!!l,e,!0):Kl(t,!!l,l?[]:"",!1));return;case"textarea":D=T=null;for(o in l)if(a=l[o],l.hasOwnProperty(o)&&a!=null&&!n.hasOwnProperty(o))switch(o){case"value":break;case"children":break;default:dt(t,e,o,null,n,a)}for(f in n)if(a=n[f],u=l[f],n.hasOwnProperty(f)&&(a!=null||u!=null))switch(f){case"value":T=a;break;case"defaultValue":D=a;break;case"children":break;case"dangerouslySetInnerHTML":if(a!=null)throw Error(s(91));break;default:a!==u&&dt(t,e,f,a,n,u)}nf(t,T,D);return;case"option":for(var V in l)if(T=l[V],l.hasOwnProperty(V)&&T!=null&&!n.hasOwnProperty(V))switch(V){case"selected":t.selected=!1;break;default:dt(t,e,V,null,n,T)}for(h in n)if(T=n[h],D=l[h],n.hasOwnProperty(h)&&T!==D&&(T!=null||D!=null))switch(h){case"selected":t.selected=T&&typeof T!="function"&&typeof T!="symbol";break;default:dt(t,e,h,T,n,D)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var X in l)T=l[X],l.hasOwnProperty(X)&&T!=null&&!n.hasOwnProperty(X)&&dt(t,e,X,null,n,T);for(S in n)if(T=n[S],D=l[S],n.hasOwnProperty(S)&&T!==D&&(T!=null||D!=null))switch(S){case"children":case"dangerouslySetInnerHTML":if(T!=null)throw Error(s(137,e));break;default:dt(t,e,S,T,n,D)}return;default:if(di(e)){for(var ht in l)T=l[ht],l.hasOwnProperty(ht)&&T!==void 0&&!n.hasOwnProperty(ht)&&Wc(t,e,ht,void 0,n,T);for(O in n)T=n[O],D=l[O],!n.hasOwnProperty(O)||T===D||T===void 0&&D===void 0||Wc(t,e,O,T,n,D);return}}for(var m in l)T=l[m],l.hasOwnProperty(m)&&T!=null&&!n.hasOwnProperty(m)&&dt(t,e,m,null,n,T);for(_ in n)T=n[_],D=l[_],!n.hasOwnProperty(_)||T===D||T==null&&D==null||dt(t,e,_,T,n,D)}var $c=null,Fc=null;function Nu(t){return t.nodeType===9?t:t.ownerDocument}function cd(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function rd(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function Pc(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var tr=null;function yv(){var t=window.event;return t&&t.type==="popstate"?t===tr?!1:(tr=t,!0):(tr=null,!1)}var fd=typeof setTimeout=="function"?setTimeout:void 0,mv=typeof clearTimeout=="function"?clearTimeout:void 0,sd=typeof Promise=="function"?Promise:void 0,pv=typeof queueMicrotask=="function"?queueMicrotask:typeof sd<"u"?function(t){return sd.resolve(null).then(t).catch(Sv)}:fd;function Sv(t){setTimeout(function(){throw t})}function dl(t){return t==="head"}function od(t,e){var l=e,n=0,a=0;do{var u=l.nextSibling;if(t.removeChild(l),u&&u.nodeType===8)if(l=u.data,l==="/$"){if(0<n&&8>n){l=n;var f=t.ownerDocument;if(l&1&&ya(f.documentElement),l&2&&ya(f.body),l&4)for(l=f.head,ya(l),f=l.firstChild;f;){var o=f.nextSibling,h=f.nodeName;f[zn]||h==="SCRIPT"||h==="STYLE"||h==="LINK"&&f.rel.toLowerCase()==="stylesheet"||l.removeChild(f),f=o}}if(a===0){t.removeChild(u),Ea(e);return}a--}else l==="$"||l==="$?"||l==="$!"?a++:n=l.charCodeAt(0)-48;else n=0;l=u}while(l);Ea(e)}function er(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var l=e;switch(e=e.nextSibling,l.nodeName){case"HTML":case"HEAD":case"BODY":er(l),ii(l);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(l.rel.toLowerCase()==="stylesheet")continue}t.removeChild(l)}}function bv(t,e,l,n){for(;t.nodeType===1;){var a=l;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!n&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(n){if(!t[zn])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(u=t.getAttribute("rel"),u==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(u!==a.rel||t.getAttribute("href")!==(a.href==null||a.href===""?null:a.href)||t.getAttribute("crossorigin")!==(a.crossOrigin==null?null:a.crossOrigin)||t.getAttribute("title")!==(a.title==null?null:a.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(u=t.getAttribute("src"),(u!==(a.src==null?null:a.src)||t.getAttribute("type")!==(a.type==null?null:a.type)||t.getAttribute("crossorigin")!==(a.crossOrigin==null?null:a.crossOrigin))&&u&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var u=a.name==null?null:""+a.name;if(a.type==="hidden"&&t.getAttribute("name")===u)return t}else return t;if(t=De(t.nextSibling),t===null)break}return null}function Tv(t,e,l){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!l||(t=De(t.nextSibling),t===null))return null;return t}function lr(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function Dv(t,e){var l=t.ownerDocument;if(t.data!=="$?"||l.readyState==="complete")e();else{var n=function(){e(),l.removeEventListener("DOMContentLoaded",n)};l.addEventListener("DOMContentLoaded",n),t._reactRetry=n}}function De(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var nr=null;function dd(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var l=t.data;if(l==="$"||l==="$!"||l==="$?"){if(e===0)return t;e--}else l==="/$"&&e++}t=t.previousSibling}return null}function hd(t,e,l){switch(e=Nu(l),t){case"html":if(t=e.documentElement,!t)throw Error(s(452));return t;case"head":if(t=e.head,!t)throw Error(s(453));return t;case"body":if(t=e.body,!t)throw Error(s(454));return t;default:throw Error(s(451))}}function ya(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);ii(t)}var ye=new Map,gd=new Set;function Uu(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var Ve=U.d;U.d={f:Ov,r:Ev,D:Av,C:Mv,L:Cv,m:_v,X:xv,S:Rv,M:zv};function Ov(){var t=Ve.f(),e=Eu();return t||e}function Ev(t){var e=Ll(t);e!==null&&e.tag===5&&e.type==="form"?Us(e):Ve.r(t)}var On=typeof document>"u"?null:document;function vd(t,e,l){var n=On;if(n&&typeof e=="string"&&e){var a=fe(e);a='link[rel="'+t+'"][href="'+a+'"]',typeof l=="string"&&(a+='[crossorigin="'+l+'"]'),gd.has(a)||(gd.add(a),t={rel:t,crossOrigin:l,href:e},n.querySelector(a)===null&&(e=n.createElement("link"),Bt(e,"link",t),zt(e),n.head.appendChild(e)))}}function Av(t){Ve.D(t),vd("dns-prefetch",t,null)}function Mv(t,e){Ve.C(t,e),vd("preconnect",t,e)}function Cv(t,e,l){Ve.L(t,e,l);var n=On;if(n&&t&&e){var a='link[rel="preload"][as="'+fe(e)+'"]';e==="image"&&l&&l.imageSrcSet?(a+='[imagesrcset="'+fe(l.imageSrcSet)+'"]',typeof l.imageSizes=="string"&&(a+='[imagesizes="'+fe(l.imageSizes)+'"]')):a+='[href="'+fe(t)+'"]';var u=a;switch(e){case"style":u=En(t);break;case"script":u=An(t)}ye.has(u)||(t=z({rel:"preload",href:e==="image"&&l&&l.imageSrcSet?void 0:t,as:e},l),ye.set(u,t),n.querySelector(a)!==null||e==="style"&&n.querySelector(ma(u))||e==="script"&&n.querySelector(pa(u))||(e=n.createElement("link"),Bt(e,"link",t),zt(e),n.head.appendChild(e)))}}function _v(t,e){Ve.m(t,e);var l=On;if(l&&t){var n=e&&typeof e.as=="string"?e.as:"script",a='link[rel="modulepreload"][as="'+fe(n)+'"][href="'+fe(t)+'"]',u=a;switch(n){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":u=An(t)}if(!ye.has(u)&&(t=z({rel:"modulepreload",href:t},e),ye.set(u,t),l.querySelector(a)===null)){switch(n){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(l.querySelector(pa(u)))return}n=l.createElement("link"),Bt(n,"link",t),zt(n),l.head.appendChild(n)}}}function Rv(t,e,l){Ve.S(t,e,l);var n=On;if(n&&t){var a=Ql(n).hoistableStyles,u=En(t);e=e||"default";var f=a.get(u);if(!f){var o={loading:0,preload:null};if(f=n.querySelector(ma(u)))o.loading=5;else{t=z({rel:"stylesheet",href:t,"data-precedence":e},l),(l=ye.get(u))&&ar(t,l);var h=f=n.createElement("link");zt(h),Bt(h,"link",t),h._p=new Promise(function(S,O){h.onload=S,h.onerror=O}),h.addEventListener("load",function(){o.loading|=1}),h.addEventListener("error",function(){o.loading|=2}),o.loading|=4,Hu(f,e,n)}f={type:"stylesheet",instance:f,count:1,state:o},a.set(u,f)}}}function xv(t,e){Ve.X(t,e);var l=On;if(l&&t){var n=Ql(l).hoistableScripts,a=An(t),u=n.get(a);u||(u=l.querySelector(pa(a)),u||(t=z({src:t,async:!0},e),(e=ye.get(a))&&ur(t,e),u=l.createElement("script"),zt(u),Bt(u,"link",t),l.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},n.set(a,u))}}function zv(t,e){Ve.M(t,e);var l=On;if(l&&t){var n=Ql(l).hoistableScripts,a=An(t),u=n.get(a);u||(u=l.querySelector(pa(a)),u||(t=z({src:t,async:!0,type:"module"},e),(e=ye.get(a))&&ur(t,e),u=l.createElement("script"),zt(u),Bt(u,"link",t),l.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},n.set(a,u))}}function yd(t,e,l,n){var a=(a=K.current)?Uu(a):null;if(!a)throw Error(s(446));switch(t){case"meta":case"title":return null;case"style":return typeof l.precedence=="string"&&typeof l.href=="string"?(e=En(l.href),l=Ql(a).hoistableStyles,n=l.get(e),n||(n={type:"style",instance:null,count:0,state:null},l.set(e,n)),n):{type:"void",instance:null,count:0,state:null};case"link":if(l.rel==="stylesheet"&&typeof l.href=="string"&&typeof l.precedence=="string"){t=En(l.href);var u=Ql(a).hoistableStyles,f=u.get(t);if(f||(a=a.ownerDocument||a,f={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(t,f),(u=a.querySelector(ma(t)))&&!u._p&&(f.instance=u,f.state.loading=5),ye.has(t)||(l={rel:"preload",as:"style",href:l.href,crossOrigin:l.crossOrigin,integrity:l.integrity,media:l.media,hrefLang:l.hrefLang,referrerPolicy:l.referrerPolicy},ye.set(t,l),u||Nv(a,t,l,f.state))),e&&n===null)throw Error(s(528,""));return f}if(e&&n!==null)throw Error(s(529,""));return null;case"script":return e=l.async,l=l.src,typeof l=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=An(l),l=Ql(a).hoistableScripts,n=l.get(e),n||(n={type:"script",instance:null,count:0,state:null},l.set(e,n)),n):{type:"void",instance:null,count:0,state:null};default:throw Error(s(444,t))}}function En(t){return'href="'+fe(t)+'"'}function ma(t){return'link[rel="stylesheet"]['+t+"]"}function md(t){return z({},t,{"data-precedence":t.precedence,precedence:null})}function Nv(t,e,l,n){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?n.loading=1:(e=t.createElement("link"),n.preload=e,e.addEventListener("load",function(){return n.loading|=1}),e.addEventListener("error",function(){return n.loading|=2}),Bt(e,"link",l),zt(e),t.head.appendChild(e))}function An(t){return'[src="'+fe(t)+'"]'}function pa(t){return"script[async]"+t}function pd(t,e,l){if(e.count++,e.instance===null)switch(e.type){case"style":var n=t.querySelector('style[data-href~="'+fe(l.href)+'"]');if(n)return e.instance=n,zt(n),n;var a=z({},l,{"data-href":l.href,"data-precedence":l.precedence,href:null,precedence:null});return n=(t.ownerDocument||t).createElement("style"),zt(n),Bt(n,"style",a),Hu(n,l.precedence,t),e.instance=n;case"stylesheet":a=En(l.href);var u=t.querySelector(ma(a));if(u)return e.state.loading|=4,e.instance=u,zt(u),u;n=md(l),(a=ye.get(a))&&ar(n,a),u=(t.ownerDocument||t).createElement("link"),zt(u);var f=u;return f._p=new Promise(function(o,h){f.onload=o,f.onerror=h}),Bt(u,"link",n),e.state.loading|=4,Hu(u,l.precedence,t),e.instance=u;case"script":return u=An(l.src),(a=t.querySelector(pa(u)))?(e.instance=a,zt(a),a):(n=l,(a=ye.get(u))&&(n=z({},l),ur(n,a)),t=t.ownerDocument||t,a=t.createElement("script"),zt(a),Bt(a,"link",n),t.head.appendChild(a),e.instance=a);case"void":return null;default:throw Error(s(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(n=e.instance,e.state.loading|=4,Hu(n,l.precedence,t));return e.instance}function Hu(t,e,l){for(var n=l.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),a=n.length?n[n.length-1]:null,u=a,f=0;f<n.length;f++){var o=n[f];if(o.dataset.precedence===e)u=o;else if(u!==a)break}u?u.parentNode.insertBefore(t,u.nextSibling):(e=l.nodeType===9?l.head:l,e.insertBefore(t,e.firstChild))}function ar(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function ur(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var qu=null;function Sd(t,e,l){if(qu===null){var n=new Map,a=qu=new Map;a.set(l,n)}else a=qu,n=a.get(l),n||(n=new Map,a.set(l,n));if(n.has(t))return n;for(n.set(t,null),l=l.getElementsByTagName(t),a=0;a<l.length;a++){var u=l[a];if(!(u[zn]||u[Lt]||t==="link"&&u.getAttribute("rel")==="stylesheet")&&u.namespaceURI!=="http://www.w3.org/2000/svg"){var f=u.getAttribute(e)||"";f=t+f;var o=n.get(f);o?o.push(u):n.set(f,[u])}}return n}function bd(t,e,l){t=t.ownerDocument||t,t.head.insertBefore(l,e==="title"?t.querySelector("head > title"):null)}function Uv(t,e,l){if(l===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function Td(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var Sa=null;function Hv(){}function qv(t,e,l){if(Sa===null)throw Error(s(475));var n=Sa;if(e.type==="stylesheet"&&(typeof l.media!="string"||matchMedia(l.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var a=En(l.href),u=t.querySelector(ma(a));if(u){t=u._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(n.count++,n=ju.bind(n),t.then(n,n)),e.state.loading|=4,e.instance=u,zt(u);return}u=t.ownerDocument||t,l=md(l),(a=ye.get(a))&&ar(l,a),u=u.createElement("link"),zt(u);var f=u;f._p=new Promise(function(o,h){f.onload=o,f.onerror=h}),Bt(u,"link",l),e.instance=u}n.stylesheets===null&&(n.stylesheets=new Map),n.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(n.count++,e=ju.bind(n),t.addEventListener("load",e),t.addEventListener("error",e))}}function jv(){if(Sa===null)throw Error(s(475));var t=Sa;return t.stylesheets&&t.count===0&&ir(t,t.stylesheets),0<t.count?function(e){var l=setTimeout(function(){if(t.stylesheets&&ir(t,t.stylesheets),t.unsuspend){var n=t.unsuspend;t.unsuspend=null,n()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(l)}}:null}function ju(){if(this.count--,this.count===0){if(this.stylesheets)ir(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var Bu=null;function ir(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,Bu=new Map,e.forEach(Bv,t),Bu=null,ju.call(t))}function Bv(t,e){if(!(e.state.loading&4)){var l=Bu.get(t);if(l)var n=l.get(null);else{l=new Map,Bu.set(t,l);for(var a=t.querySelectorAll("link[data-precedence],style[data-precedence]"),u=0;u<a.length;u++){var f=a[u];(f.nodeName==="LINK"||f.getAttribute("media")!=="not all")&&(l.set(f.dataset.precedence,f),n=f)}n&&l.set(null,n)}a=e.instance,f=a.getAttribute("data-precedence"),u=l.get(f)||n,u===n&&l.set(null,a),l.set(f,a),this.count++,n=ju.bind(this),a.addEventListener("load",n),a.addEventListener("error",n),u?u.parentNode.insertBefore(a,u.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(a,t.firstChild)),e.state.loading|=4}}var ba={$$typeof:mt,Provider:null,Consumer:null,_currentValue:Z,_currentValue2:Z,_threadCount:0};function Yv(t,e,l,n,a,u,f,o){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=li(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=li(0),this.hiddenUpdates=li(null),this.identifierPrefix=n,this.onUncaughtError=a,this.onCaughtError=u,this.onRecoverableError=f,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=o,this.incompleteTransitions=new Map}function Dd(t,e,l,n,a,u,f,o,h,S,O,_){return t=new Yv(t,e,l,f,o,h,S,_),e=1,u===!0&&(e|=24),u=le(3,null,null,e),t.current=u,u.stateNode=t,e=Xi(),e.refCount++,t.pooledCache=e,e.refCount++,u.memoizedState={element:n,isDehydrated:l,cache:e},Vi(u),t}function Od(t){return t?(t=en,t):en}function Ed(t,e,l,n,a,u){a=Od(a),n.context===null?n.context=a:n.pendingContext=a,n=Pe(e),n.payload={element:l},u=u===void 0?null:u,u!==null&&(n.callback=u),l=tl(t,n,e),l!==null&&(ce(l,t,e),Wn(l,t,e))}function Ad(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var l=t.retryLane;t.retryLane=l!==0&&l<e?l:e}}function cr(t,e){Ad(t,e),(t=t.alternate)&&Ad(t,e)}function Md(t){if(t.tag===13){var e=tn(t,67108864);e!==null&&ce(e,t,67108864),cr(t,67108864)}}var Yu=!0;function Gv(t,e,l,n){var a=E.T;E.T=null;var u=U.p;try{U.p=2,rr(t,e,l,n)}finally{U.p=u,E.T=a}}function wv(t,e,l,n){var a=E.T;E.T=null;var u=U.p;try{U.p=8,rr(t,e,l,n)}finally{U.p=u,E.T=a}}function rr(t,e,l,n){if(Yu){var a=fr(n);if(a===null)Ic(t,e,n,Gu,l),_d(t,n);else if(Lv(a,t,e,l,n))n.stopPropagation();else if(_d(t,n),e&4&&-1<Xv.indexOf(t)){for(;a!==null;){var u=Ll(a);if(u!==null)switch(u.tag){case 3:if(u=u.stateNode,u.current.memoizedState.isDehydrated){var f=bl(u.pendingLanes);if(f!==0){var o=u;for(o.pendingLanes|=2,o.entangledLanes|=2;f;){var h=1<<31-te(f);o.entanglements[1]|=h,f&=~h}Re(u),(rt&6)===0&&(Du=Ee()+500,ha(0))}}break;case 13:o=tn(u,2),o!==null&&ce(o,u,2),Eu(),cr(u,2)}if(u=fr(n),u===null&&Ic(t,e,n,Gu,l),u===a)break;a=u}a!==null&&n.stopPropagation()}else Ic(t,e,n,null,l)}}function fr(t){return t=gi(t),sr(t)}var Gu=null;function sr(t){if(Gu=null,t=Xl(t),t!==null){var e=y(t);if(e===null)t=null;else{var l=e.tag;if(l===13){if(t=A(e),t!==null)return t;t=null}else if(l===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return Gu=t,null}function Cd(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Mh()){case Gr:return 2;case wr:return 8;case za:case Ch:return 32;case Xr:return 268435456;default:return 32}default:return 32}}var or=!1,hl=null,gl=null,vl=null,Ta=new Map,Da=new Map,yl=[],Xv="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function _d(t,e){switch(t){case"focusin":case"focusout":hl=null;break;case"dragenter":case"dragleave":gl=null;break;case"mouseover":case"mouseout":vl=null;break;case"pointerover":case"pointerout":Ta.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":Da.delete(e.pointerId)}}function Oa(t,e,l,n,a,u){return t===null||t.nativeEvent!==u?(t={blockedOn:e,domEventName:l,eventSystemFlags:n,nativeEvent:u,targetContainers:[a]},e!==null&&(e=Ll(e),e!==null&&Md(e)),t):(t.eventSystemFlags|=n,e=t.targetContainers,a!==null&&e.indexOf(a)===-1&&e.push(a),t)}function Lv(t,e,l,n,a){switch(e){case"focusin":return hl=Oa(hl,t,e,l,n,a),!0;case"dragenter":return gl=Oa(gl,t,e,l,n,a),!0;case"mouseover":return vl=Oa(vl,t,e,l,n,a),!0;case"pointerover":var u=a.pointerId;return Ta.set(u,Oa(Ta.get(u)||null,t,e,l,n,a)),!0;case"gotpointercapture":return u=a.pointerId,Da.set(u,Oa(Da.get(u)||null,t,e,l,n,a)),!0}return!1}function Rd(t){var e=Xl(t.target);if(e!==null){var l=y(e);if(l!==null){if(e=l.tag,e===13){if(e=A(l),e!==null){t.blockedOn=e,qh(t.priority,function(){if(l.tag===13){var n=ie();n=ni(n);var a=tn(l,n);a!==null&&ce(a,l,n),cr(l,n)}});return}}else if(e===3&&l.stateNode.current.memoizedState.isDehydrated){t.blockedOn=l.tag===3?l.stateNode.containerInfo:null;return}}}t.blockedOn=null}function wu(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var l=fr(t.nativeEvent);if(l===null){l=t.nativeEvent;var n=new l.constructor(l.type,l);hi=n,l.target.dispatchEvent(n),hi=null}else return e=Ll(l),e!==null&&Md(e),t.blockedOn=l,!1;e.shift()}return!0}function xd(t,e,l){wu(t)&&l.delete(e)}function Qv(){or=!1,hl!==null&&wu(hl)&&(hl=null),gl!==null&&wu(gl)&&(gl=null),vl!==null&&wu(vl)&&(vl=null),Ta.forEach(xd),Da.forEach(xd)}function Xu(t,e){t.blockedOn===e&&(t.blockedOn=null,or||(or=!0,c.unstable_scheduleCallback(c.unstable_NormalPriority,Qv)))}var Lu=null;function zd(t){Lu!==t&&(Lu=t,c.unstable_scheduleCallback(c.unstable_NormalPriority,function(){Lu===t&&(Lu=null);for(var e=0;e<t.length;e+=3){var l=t[e],n=t[e+1],a=t[e+2];if(typeof n!="function"){if(sr(n||l)===null)continue;break}var u=Ll(l);u!==null&&(t.splice(e,3),e-=3,sc(u,{pending:!0,data:a,method:l.method,action:n},n,a))}}))}function Ea(t){function e(h){return Xu(h,t)}hl!==null&&Xu(hl,t),gl!==null&&Xu(gl,t),vl!==null&&Xu(vl,t),Ta.forEach(e),Da.forEach(e);for(var l=0;l<yl.length;l++){var n=yl[l];n.blockedOn===t&&(n.blockedOn=null)}for(;0<yl.length&&(l=yl[0],l.blockedOn===null);)Rd(l),l.blockedOn===null&&yl.shift();if(l=(t.ownerDocument||t).$$reactFormReplay,l!=null)for(n=0;n<l.length;n+=3){var a=l[n],u=l[n+1],f=a[Jt]||null;if(typeof u=="function")f||zd(l);else if(f){var o=null;if(u&&u.hasAttribute("formAction")){if(a=u,f=u[Jt]||null)o=f.formAction;else if(sr(a)!==null)continue}else o=f.action;typeof o=="function"?l[n+1]=o:(l.splice(n,3),n-=3),zd(l)}}}function dr(t){this._internalRoot=t}Qu.prototype.render=dr.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(s(409));var l=e.current,n=ie();Ed(l,n,t,e,null,null)},Qu.prototype.unmount=dr.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;Ed(t.current,2,null,t,null,null),Eu(),e[wl]=null}};function Qu(t){this._internalRoot=t}Qu.prototype.unstable_scheduleHydration=function(t){if(t){var e=Kr();t={blockedOn:null,target:t,priority:e};for(var l=0;l<yl.length&&e!==0&&e<yl[l].priority;l++);yl.splice(l,0,t),l===0&&Rd(t)}};var Nd=i.version;if(Nd!=="19.1.0")throw Error(s(527,Nd,"19.1.0"));U.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(s(188)):(t=Object.keys(t).join(","),Error(s(268,t)));return t=C(e),t=t!==null?b(t):null,t=t===null?null:t.stateNode,t};var Zv={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:E,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Zu=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Zu.isDisabled&&Zu.supportsFiber)try{_n=Zu.inject(Zv),Pt=Zu}catch{}}return Ma.createRoot=function(t,e){if(!d(t))throw Error(s(299));var l=!1,n="",a=Js,u=ks,f=Is,o=null;return e!=null&&(e.unstable_strictMode===!0&&(l=!0),e.identifierPrefix!==void 0&&(n=e.identifierPrefix),e.onUncaughtError!==void 0&&(a=e.onUncaughtError),e.onCaughtError!==void 0&&(u=e.onCaughtError),e.onRecoverableError!==void 0&&(f=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(o=e.unstable_transitionCallbacks)),e=Dd(t,1,!1,null,null,l,n,a,u,f,o,null),t[wl]=e.current,kc(t),new dr(e)},Ma.hydrateRoot=function(t,e,l){if(!d(t))throw Error(s(299));var n=!1,a="",u=Js,f=ks,o=Is,h=null,S=null;return l!=null&&(l.unstable_strictMode===!0&&(n=!0),l.identifierPrefix!==void 0&&(a=l.identifierPrefix),l.onUncaughtError!==void 0&&(u=l.onUncaughtError),l.onCaughtError!==void 0&&(f=l.onCaughtError),l.onRecoverableError!==void 0&&(o=l.onRecoverableError),l.unstable_transitionCallbacks!==void 0&&(h=l.unstable_transitionCallbacks),l.formState!==void 0&&(S=l.formState)),e=Dd(t,1,!0,e,l??null,n,a,u,f,o,h,S),e.context=Od(null),l=e.current,n=ie(),n=ni(n),a=Pe(n),a.callback=null,tl(l,a,n),l=n,e.current.lanes=l,xn(e,l),Re(e),t[wl]=e.current,kc(t),new Qu(e)},Ma.version="19.1.0",Ma}var Ld;function ey(){if(Ld)return vr.exports;Ld=1;function c(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(c)}catch(i){console.error(i)}}return c(),vr.exports=ty(),vr.exports}var ly=ey();const Ra=P.createContext(),ah=({children:c})=>{const[i,r]=P.useState([]),[s,d]=P.useState(null),y=R=>{r([...i,R])},A=(R,C)=>{r(i.map(b=>b.id===R?{...b,props:C}:b))};return H.jsx(Ra.Provider,{value:{components:i,addComponent:y,updateComponent:A,selectedComponent:s,setSelectedComponent:d},children:c})},uh=P.createContext({dragDropManager:void 0});function me(c){return"Minified Redux error #"+c+"; visit https://redux.js.org/Errors?code="+c+" for the full message or use the non-minified dev environment for full errors. "}var Qd=function(){return typeof Symbol=="function"&&Symbol.observable||"@@observable"}(),Zd=function(){return Math.random().toString(36).substring(7).split("").join(".")},Vd={INIT:"@@redux/INIT"+Zd(),REPLACE:"@@redux/REPLACE"+Zd()};function ny(c){if(typeof c!="object"||c===null)return!1;for(var i=c;Object.getPrototypeOf(i)!==null;)i=Object.getPrototypeOf(i);return Object.getPrototypeOf(c)===i}function ih(c,i,r){var s;if(typeof i=="function"&&typeof r=="function"||typeof r=="function"&&typeof arguments[3]=="function")throw new Error(me(0));if(typeof i=="function"&&typeof r>"u"&&(r=i,i=void 0),typeof r<"u"){if(typeof r!="function")throw new Error(me(1));return r(ih)(c,i)}if(typeof c!="function")throw new Error(me(2));var d=c,y=i,A=[],R=A,C=!1;function b(){R===A&&(R=A.slice())}function z(){if(C)throw new Error(me(3));return y}function L(k){if(typeof k!="function")throw new Error(me(4));if(C)throw new Error(me(5));var lt=!0;return b(),R.push(k),function(){if(lt){if(C)throw new Error(me(6));lt=!1,b();var st=R.indexOf(k);R.splice(st,1),A=null}}}function G(k){if(!ny(k))throw new Error(me(7));if(typeof k.type>"u")throw new Error(me(8));if(C)throw new Error(me(9));try{C=!0,y=d(y,k)}finally{C=!1}for(var lt=A=R,yt=0;yt<lt.length;yt++){var st=lt[yt];st()}return k}function B(k){if(typeof k!="function")throw new Error(me(10));d=k,G({type:Vd.REPLACE})}function nt(){var k,lt=L;return k={subscribe:function(st){if(typeof st!="object"||st===null)throw new Error(me(11));function mt(){st.next&&st.next(z())}mt();var Yt=lt(mt);return{unsubscribe:Yt}}},k[Qd]=function(){return this},k}return G({type:Vd.INIT}),s={dispatch:G,subscribe:L,getState:z,replaceReducer:B},s[Qd]=nt,s}function J(c,i,...r){if(ay()&&i===void 0)throw new Error("invariant requires an error message argument");if(!c){let s;if(i===void 0)s=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{let d=0;s=new Error(i.replace(/%s/g,function(){return r[d++]})),s.name="Invariant Violation"}throw s.framesToPop=1,s}}function ay(){return typeof process<"u"&&!0}function uy(c,i,r){return i.split(".").reduce((s,d)=>s&&s[d]?s[d]:r||null,c)}function iy(c,i){return c.filter(r=>r!==i)}function ch(c){return typeof c=="object"}function cy(c,i){const r=new Map,s=y=>{r.set(y,r.has(y)?r.get(y)+1:1)};c.forEach(s),i.forEach(s);const d=[];return r.forEach((y,A)=>{y===1&&d.push(A)}),d}function ry(c,i){return c.filter(r=>i.indexOf(r)>-1)}const Nr="dnd-core/INIT_COORDS",Ju="dnd-core/BEGIN_DRAG",Ur="dnd-core/PUBLISH_DRAG_SOURCE",ku="dnd-core/HOVER",Iu="dnd-core/DROP",Wu="dnd-core/END_DRAG";function Kd(c,i){return{type:Nr,payload:{sourceClientOffset:i||null,clientOffset:c||null}}}const fy={type:Nr,payload:{clientOffset:null,sourceClientOffset:null}};function sy(c){return function(r=[],s={publishSource:!0}){const{publishSource:d=!0,clientOffset:y,getSourceClientOffset:A}=s,R=c.getMonitor(),C=c.getRegistry();c.dispatch(Kd(y)),oy(r,R,C);const b=gy(r,R);if(b==null){c.dispatch(fy);return}let z=null;if(y){if(!A)throw new Error("getSourceClientOffset must be defined");dy(A),z=A(b)}c.dispatch(Kd(y,z));const G=C.getSource(b).beginDrag(R,b);if(G==null)return;hy(G),C.pinSource(b);const B=C.getSourceType(b);return{type:Ju,payload:{itemType:B,item:G,sourceId:b,clientOffset:y||null,sourceClientOffset:z||null,isSourcePublic:!!d}}}}function oy(c,i,r){J(!i.isDragging(),"Cannot call beginDrag while dragging."),c.forEach(function(s){J(r.getSource(s),"Expected sourceIds to be registered.")})}function dy(c){J(typeof c=="function","When clientOffset is provided, getSourceClientOffset must be a function.")}function hy(c){J(ch(c),"Item must be an object.")}function gy(c,i){let r=null;for(let s=c.length-1;s>=0;s--)if(i.canDragSource(c[s])){r=c[s];break}return r}function vy(c,i,r){return i in c?Object.defineProperty(c,i,{value:r,enumerable:!0,configurable:!0,writable:!0}):c[i]=r,c}function yy(c){for(var i=1;i<arguments.length;i++){var r=arguments[i]!=null?arguments[i]:{},s=Object.keys(r);typeof Object.getOwnPropertySymbols=="function"&&(s=s.concat(Object.getOwnPropertySymbols(r).filter(function(d){return Object.getOwnPropertyDescriptor(r,d).enumerable}))),s.forEach(function(d){vy(c,d,r[d])})}return c}function my(c){return function(r={}){const s=c.getMonitor(),d=c.getRegistry();py(s),Ty(s).forEach((A,R)=>{const C=Sy(A,R,d,s),b={type:Iu,payload:{dropResult:yy({},r,C)}};c.dispatch(b)})}}function py(c){J(c.isDragging(),"Cannot call drop while not dragging."),J(!c.didDrop(),"Cannot call drop twice during one drag operation.")}function Sy(c,i,r,s){const d=r.getTarget(c);let y=d?d.drop(s,c):void 0;return by(y),typeof y>"u"&&(y=i===0?{}:s.getDropResult()),y}function by(c){J(typeof c>"u"||ch(c),"Drop result must either be an object or undefined.")}function Ty(c){const i=c.getTargetIds().filter(c.canDropOnTarget,c);return i.reverse(),i}function Dy(c){return function(){const r=c.getMonitor(),s=c.getRegistry();Oy(r);const d=r.getSourceId();return d!=null&&(s.getSource(d,!0).endDrag(r,d),s.unpinSource()),{type:Wu}}}function Oy(c){J(c.isDragging(),"Cannot call endDrag while not dragging.")}function Mr(c,i){return i===null?c===null:Array.isArray(c)?c.some(r=>r===i):c===i}function Ey(c){return function(r,{clientOffset:s}={}){Ay(r);const d=r.slice(0),y=c.getMonitor(),A=c.getRegistry(),R=y.getItemType();return Cy(d,A,R),My(d,y,A),_y(d,y,A),{type:ku,payload:{targetIds:d,clientOffset:s||null}}}}function Ay(c){J(Array.isArray(c),"Expected targetIds to be an array.")}function My(c,i,r){J(i.isDragging(),"Cannot call hover while not dragging."),J(!i.didDrop(),"Cannot call hover after drop.");for(let s=0;s<c.length;s++){const d=c[s];J(c.lastIndexOf(d)===s,"Expected targetIds to be unique in the passed array.");const y=r.getTarget(d);J(y,"Expected targetIds to be registered.")}}function Cy(c,i,r){for(let s=c.length-1;s>=0;s--){const d=c[s],y=i.getTargetType(d);Mr(y,r)||c.splice(s,1)}}function _y(c,i,r){c.forEach(function(s){r.getTarget(s).hover(i,s)})}function Ry(c){return function(){if(c.getMonitor().isDragging())return{type:Ur}}}function xy(c){return{beginDrag:sy(c),publishDragSource:Ry(c),hover:Ey(c),drop:my(c),endDrag:Dy(c)}}class zy{receiveBackend(i){this.backend=i}getMonitor(){return this.monitor}getBackend(){return this.backend}getRegistry(){return this.monitor.registry}getActions(){const i=this,{dispatch:r}=this.store;function s(y){return(...A)=>{const R=y.apply(i,A);typeof R<"u"&&r(R)}}const d=xy(this);return Object.keys(d).reduce((y,A)=>{const R=d[A];return y[A]=s(R),y},{})}dispatch(i){this.store.dispatch(i)}constructor(i,r){this.isSetUp=!1,this.handleRefCountChange=()=>{const s=this.store.getState().refCount>0;this.backend&&(s&&!this.isSetUp?(this.backend.setup(),this.isSetUp=!0):!s&&this.isSetUp&&(this.backend.teardown(),this.isSetUp=!1))},this.store=i,this.monitor=r,i.subscribe(this.handleRefCountChange)}}function Ny(c,i){return{x:c.x+i.x,y:c.y+i.y}}function rh(c,i){return{x:c.x-i.x,y:c.y-i.y}}function Uy(c){const{clientOffset:i,initialClientOffset:r,initialSourceClientOffset:s}=c;return!i||!r||!s?null:rh(Ny(i,s),r)}function Hy(c){const{clientOffset:i,initialClientOffset:r}=c;return!i||!r?null:rh(i,r)}const _a=[],Hr=[];_a.__IS_NONE__=!0;Hr.__IS_ALL__=!0;function qy(c,i){return c===_a?!1:c===Hr||typeof i>"u"?!0:ry(i,c).length>0}class jy{subscribeToStateChange(i,r={}){const{handlerIds:s}=r;J(typeof i=="function","listener must be a function."),J(typeof s>"u"||Array.isArray(s),"handlerIds, when specified, must be an array of strings.");let d=this.store.getState().stateId;const y=()=>{const A=this.store.getState(),R=A.stateId;try{R===d||R===d+1&&!qy(A.dirtyHandlerIds,s)||i()}finally{d=R}};return this.store.subscribe(y)}subscribeToOffsetChange(i){J(typeof i=="function","listener must be a function.");let r=this.store.getState().dragOffset;const s=()=>{const d=this.store.getState().dragOffset;d!==r&&(r=d,i())};return this.store.subscribe(s)}canDragSource(i){if(!i)return!1;const r=this.registry.getSource(i);return J(r,`Expected to find a valid source. sourceId=${i}`),this.isDragging()?!1:r.canDrag(this,i)}canDropOnTarget(i){if(!i)return!1;const r=this.registry.getTarget(i);if(J(r,`Expected to find a valid target. targetId=${i}`),!this.isDragging()||this.didDrop())return!1;const s=this.registry.getTargetType(i),d=this.getItemType();return Mr(s,d)&&r.canDrop(this,i)}isDragging(){return!!this.getItemType()}isDraggingSource(i){if(!i)return!1;const r=this.registry.getSource(i,!0);if(J(r,`Expected to find a valid source. sourceId=${i}`),!this.isDragging()||!this.isSourcePublic())return!1;const s=this.registry.getSourceType(i),d=this.getItemType();return s!==d?!1:r.isDragging(this,i)}isOverTarget(i,r={shallow:!1}){if(!i)return!1;const{shallow:s}=r;if(!this.isDragging())return!1;const d=this.registry.getTargetType(i),y=this.getItemType();if(y&&!Mr(d,y))return!1;const A=this.getTargetIds();if(!A.length)return!1;const R=A.indexOf(i);return s?R===A.length-1:R>-1}getItemType(){return this.store.getState().dragOperation.itemType}getItem(){return this.store.getState().dragOperation.item}getSourceId(){return this.store.getState().dragOperation.sourceId}getTargetIds(){return this.store.getState().dragOperation.targetIds}getDropResult(){return this.store.getState().dragOperation.dropResult}didDrop(){return this.store.getState().dragOperation.didDrop}isSourcePublic(){return!!this.store.getState().dragOperation.isSourcePublic}getInitialClientOffset(){return this.store.getState().dragOffset.initialClientOffset}getInitialSourceClientOffset(){return this.store.getState().dragOffset.initialSourceClientOffset}getClientOffset(){return this.store.getState().dragOffset.clientOffset}getSourceClientOffset(){return Uy(this.store.getState().dragOffset)}getDifferenceFromInitialOffset(){return Hy(this.store.getState().dragOffset)}constructor(i,r){this.store=i,this.registry=r}}const Jd=typeof global<"u"?global:self,fh=Jd.MutationObserver||Jd.WebKitMutationObserver;function sh(c){return function(){const r=setTimeout(d,0),s=setInterval(d,50);function d(){clearTimeout(r),clearInterval(s),c()}}}function By(c){let i=1;const r=new fh(c),s=document.createTextNode("");return r.observe(s,{characterData:!0}),function(){i=-i,s.data=i}}const Yy=typeof fh=="function"?By:sh;class Gy{enqueueTask(i){const{queue:r,requestFlush:s}=this;r.length||(s(),this.flushing=!0),r[r.length]=i}constructor(){this.queue=[],this.pendingErrors=[],this.flushing=!1,this.index=0,this.capacity=1024,this.flush=()=>{const{queue:i}=this;for(;this.index<i.length;){const r=this.index;if(this.index++,i[r].call(),this.index>this.capacity){for(let s=0,d=i.length-this.index;s<d;s++)i[s]=i[s+this.index];i.length-=this.index,this.index=0}}i.length=0,this.index=0,this.flushing=!1},this.registerPendingError=i=>{this.pendingErrors.push(i),this.requestErrorThrow()},this.requestFlush=Yy(this.flush),this.requestErrorThrow=sh(()=>{if(this.pendingErrors.length)throw this.pendingErrors.shift()})}}class wy{call(){try{this.task&&this.task()}catch(i){this.onError(i)}finally{this.task=null,this.release(this)}}constructor(i,r){this.onError=i,this.release=r,this.task=null}}class Xy{create(i){const r=this.freeTasks,s=r.length?r.pop():new wy(this.onError,d=>r[r.length]=d);return s.task=i,s}constructor(i){this.onError=i,this.freeTasks=[]}}const oh=new Gy,Ly=new Xy(oh.registerPendingError);function Qy(c){oh.enqueueTask(Ly.create(c))}const qr="dnd-core/ADD_SOURCE",jr="dnd-core/ADD_TARGET",Br="dnd-core/REMOVE_SOURCE",$u="dnd-core/REMOVE_TARGET";function Zy(c){return{type:qr,payload:{sourceId:c}}}function Vy(c){return{type:jr,payload:{targetId:c}}}function Ky(c){return{type:Br,payload:{sourceId:c}}}function Jy(c){return{type:$u,payload:{targetId:c}}}function ky(c){J(typeof c.canDrag=="function","Expected canDrag to be a function."),J(typeof c.beginDrag=="function","Expected beginDrag to be a function."),J(typeof c.endDrag=="function","Expected endDrag to be a function.")}function Iy(c){J(typeof c.canDrop=="function","Expected canDrop to be a function."),J(typeof c.hover=="function","Expected hover to be a function."),J(typeof c.drop=="function","Expected beginDrag to be a function.")}function Cr(c,i){if(i&&Array.isArray(c)){c.forEach(r=>Cr(r,!1));return}J(typeof c=="string"||typeof c=="symbol",i?"Type can only be a string, a symbol, or an array of either.":"Type can only be a string or a symbol.")}var Se;(function(c){c.SOURCE="SOURCE",c.TARGET="TARGET"})(Se||(Se={}));let Wy=0;function $y(){return Wy++}function Fy(c){const i=$y().toString();switch(c){case Se.SOURCE:return`S${i}`;case Se.TARGET:return`T${i}`;default:throw new Error(`Unknown Handler Role: ${c}`)}}function kd(c){switch(c[0]){case"S":return Se.SOURCE;case"T":return Se.TARGET;default:throw new Error(`Cannot parse handler ID: ${c}`)}}function Id(c,i){const r=c.entries();let s=!1;do{const{done:d,value:[,y]}=r.next();if(y===i)return!0;s=!!d}while(!s);return!1}class Py{addSource(i,r){Cr(i),ky(r);const s=this.addHandler(Se.SOURCE,i,r);return this.store.dispatch(Zy(s)),s}addTarget(i,r){Cr(i,!0),Iy(r);const s=this.addHandler(Se.TARGET,i,r);return this.store.dispatch(Vy(s)),s}containsHandler(i){return Id(this.dragSources,i)||Id(this.dropTargets,i)}getSource(i,r=!1){return J(this.isSourceId(i),"Expected a valid source ID."),r&&i===this.pinnedSourceId?this.pinnedSource:this.dragSources.get(i)}getTarget(i){return J(this.isTargetId(i),"Expected a valid target ID."),this.dropTargets.get(i)}getSourceType(i){return J(this.isSourceId(i),"Expected a valid source ID."),this.types.get(i)}getTargetType(i){return J(this.isTargetId(i),"Expected a valid target ID."),this.types.get(i)}isSourceId(i){return kd(i)===Se.SOURCE}isTargetId(i){return kd(i)===Se.TARGET}removeSource(i){J(this.getSource(i),"Expected an existing source."),this.store.dispatch(Ky(i)),Qy(()=>{this.dragSources.delete(i),this.types.delete(i)})}removeTarget(i){J(this.getTarget(i),"Expected an existing target."),this.store.dispatch(Jy(i)),this.dropTargets.delete(i),this.types.delete(i)}pinSource(i){const r=this.getSource(i);J(r,"Expected an existing source."),this.pinnedSourceId=i,this.pinnedSource=r}unpinSource(){J(this.pinnedSource,"No source is pinned at the time."),this.pinnedSourceId=null,this.pinnedSource=null}addHandler(i,r,s){const d=Fy(i);return this.types.set(d,r),i===Se.SOURCE?this.dragSources.set(d,s):i===Se.TARGET&&this.dropTargets.set(d,s),d}constructor(i){this.types=new Map,this.dragSources=new Map,this.dropTargets=new Map,this.pinnedSourceId=null,this.pinnedSource=null,this.store=i}}const t0=(c,i)=>c===i;function e0(c,i){return!c&&!i?!0:!c||!i?!1:c.x===i.x&&c.y===i.y}function l0(c,i,r=t0){if(c.length!==i.length)return!1;for(let s=0;s<c.length;++s)if(!r(c[s],i[s]))return!1;return!0}function n0(c=_a,i){switch(i.type){case ku:break;case qr:case jr:case $u:case Br:return _a;case Ju:case Ur:case Wu:case Iu:default:return Hr}const{targetIds:r=[],prevTargetIds:s=[]}=i.payload,d=cy(r,s);if(!(d.length>0||!l0(r,s)))return _a;const A=s[s.length-1],R=r[r.length-1];return A!==R&&(A&&d.push(A),R&&d.push(R)),d}function a0(c,i,r){return i in c?Object.defineProperty(c,i,{value:r,enumerable:!0,configurable:!0,writable:!0}):c[i]=r,c}function u0(c){for(var i=1;i<arguments.length;i++){var r=arguments[i]!=null?arguments[i]:{},s=Object.keys(r);typeof Object.getOwnPropertySymbols=="function"&&(s=s.concat(Object.getOwnPropertySymbols(r).filter(function(d){return Object.getOwnPropertyDescriptor(r,d).enumerable}))),s.forEach(function(d){a0(c,d,r[d])})}return c}const Wd={initialSourceClientOffset:null,initialClientOffset:null,clientOffset:null};function i0(c=Wd,i){const{payload:r}=i;switch(i.type){case Nr:case Ju:return{initialSourceClientOffset:r.sourceClientOffset,initialClientOffset:r.clientOffset,clientOffset:r.clientOffset};case ku:return e0(c.clientOffset,r.clientOffset)?c:u0({},c,{clientOffset:r.clientOffset});case Wu:case Iu:return Wd;default:return c}}function c0(c,i,r){return i in c?Object.defineProperty(c,i,{value:r,enumerable:!0,configurable:!0,writable:!0}):c[i]=r,c}function Mn(c){for(var i=1;i<arguments.length;i++){var r=arguments[i]!=null?arguments[i]:{},s=Object.keys(r);typeof Object.getOwnPropertySymbols=="function"&&(s=s.concat(Object.getOwnPropertySymbols(r).filter(function(d){return Object.getOwnPropertyDescriptor(r,d).enumerable}))),s.forEach(function(d){c0(c,d,r[d])})}return c}const r0={itemType:null,item:null,sourceId:null,targetIds:[],dropResult:null,didDrop:!1,isSourcePublic:null};function f0(c=r0,i){const{payload:r}=i;switch(i.type){case Ju:return Mn({},c,{itemType:r.itemType,item:r.item,sourceId:r.sourceId,isSourcePublic:r.isSourcePublic,dropResult:null,didDrop:!1});case Ur:return Mn({},c,{isSourcePublic:!0});case ku:return Mn({},c,{targetIds:r.targetIds});case $u:return c.targetIds.indexOf(r.targetId)===-1?c:Mn({},c,{targetIds:iy(c.targetIds,r.targetId)});case Iu:return Mn({},c,{dropResult:r.dropResult,didDrop:!0,targetIds:[]});case Wu:return Mn({},c,{itemType:null,item:null,sourceId:null,dropResult:null,didDrop:!1,isSourcePublic:null,targetIds:[]});default:return c}}function s0(c=0,i){switch(i.type){case qr:case jr:return c+1;case Br:case $u:return c-1;default:return c}}function o0(c=0){return c+1}function d0(c,i,r){return i in c?Object.defineProperty(c,i,{value:r,enumerable:!0,configurable:!0,writable:!0}):c[i]=r,c}function h0(c){for(var i=1;i<arguments.length;i++){var r=arguments[i]!=null?arguments[i]:{},s=Object.keys(r);typeof Object.getOwnPropertySymbols=="function"&&(s=s.concat(Object.getOwnPropertySymbols(r).filter(function(d){return Object.getOwnPropertyDescriptor(r,d).enumerable}))),s.forEach(function(d){d0(c,d,r[d])})}return c}function g0(c={},i){return{dirtyHandlerIds:n0(c.dirtyHandlerIds,{type:i.type,payload:h0({},i.payload,{prevTargetIds:uy(c,"dragOperation.targetIds",[])})}),dragOffset:i0(c.dragOffset,i),refCount:s0(c.refCount,i),dragOperation:f0(c.dragOperation,i),stateId:o0(c.stateId)}}function v0(c,i=void 0,r={},s=!1){const d=y0(s),y=new jy(d,new Py(d)),A=new zy(d,y),R=c(A,i,r);return A.receiveBackend(R),A}function y0(c){const i=typeof window<"u"&&window.__REDUX_DEVTOOLS_EXTENSION__;return ih(g0,c&&i&&i({name:"dnd-core",instanceId:"dnd-core"}))}function m0(c,i){if(c==null)return{};var r=p0(c,i),s,d;if(Object.getOwnPropertySymbols){var y=Object.getOwnPropertySymbols(c);for(d=0;d<y.length;d++)s=y[d],!(i.indexOf(s)>=0)&&Object.prototype.propertyIsEnumerable.call(c,s)&&(r[s]=c[s])}return r}function p0(c,i){if(c==null)return{};var r={},s=Object.keys(c),d,y;for(y=0;y<s.length;y++)d=s[y],!(i.indexOf(d)>=0)&&(r[d]=c[d]);return r}let $d=0;const Ku=Symbol.for("__REACT_DND_CONTEXT_INSTANCE__");var S0=P.memo(function(i){var{children:r}=i,s=m0(i,["children"]);const[d,y]=b0(s);return P.useEffect(()=>{if(y){const A=dh();return++$d,()=>{--$d===0&&(A[Ku]=null)}}},[]),H.jsx(uh.Provider,{value:d,children:r})});function b0(c){if("manager"in c)return[{dragDropManager:c.manager},!1];const i=T0(c.backend,c.context,c.options,c.debugMode),r=!c.context;return[i,r]}function T0(c,i=dh(),r,s){const d=i;return d[Ku]||(d[Ku]={dragDropManager:v0(c,i,r,s)}),d[Ku]}function dh(){return typeof global<"u"?global:window}var Sr,Fd;function D0(){return Fd||(Fd=1,Sr=function c(i,r){if(i===r)return!0;if(i&&r&&typeof i=="object"&&typeof r=="object"){if(i.constructor!==r.constructor)return!1;var s,d,y;if(Array.isArray(i)){if(s=i.length,s!=r.length)return!1;for(d=s;d--!==0;)if(!c(i[d],r[d]))return!1;return!0}if(i.constructor===RegExp)return i.source===r.source&&i.flags===r.flags;if(i.valueOf!==Object.prototype.valueOf)return i.valueOf()===r.valueOf();if(i.toString!==Object.prototype.toString)return i.toString()===r.toString();if(y=Object.keys(i),s=y.length,s!==Object.keys(r).length)return!1;for(d=s;d--!==0;)if(!Object.prototype.hasOwnProperty.call(r,y[d]))return!1;for(d=s;d--!==0;){var A=y[d];if(!c(i[A],r[A]))return!1}return!0}return i!==i&&r!==r}),Sr}var O0=D0();const E0=Kv(O0),Yl=typeof window<"u"?P.useLayoutEffect:P.useEffect;function A0(c,i,r){const[s,d]=P.useState(()=>i(c)),y=P.useCallback(()=>{const A=i(c);E0(s,A)||(d(A),r&&r())},[s,c,r]);return Yl(y),[s,y]}function M0(c,i,r){const[s,d]=A0(c,i,r);return Yl(function(){const A=c.getHandlerId();if(A!=null)return c.subscribeToStateChange(d,{handlerIds:[A]})},[c,d]),s}function hh(c,i,r){return M0(i,c||(()=>({})),()=>r.reconnect())}function gh(c,i){const r=[...i||[]];return i==null&&typeof c!="function"&&r.push(c),P.useMemo(()=>typeof c=="function"?c():c,r)}function C0(c){return P.useMemo(()=>c.hooks.dragSource(),[c])}function _0(c){return P.useMemo(()=>c.hooks.dragPreview(),[c])}let br=!1,Tr=!1;class R0{receiveHandlerId(i){this.sourceId=i}getHandlerId(){return this.sourceId}canDrag(){J(!br,"You may not call monitor.canDrag() inside your canDrag() implementation. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor");try{return br=!0,this.internalMonitor.canDragSource(this.sourceId)}finally{br=!1}}isDragging(){if(!this.sourceId)return!1;J(!Tr,"You may not call monitor.isDragging() inside your isDragging() implementation. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor");try{return Tr=!0,this.internalMonitor.isDraggingSource(this.sourceId)}finally{Tr=!1}}subscribeToStateChange(i,r){return this.internalMonitor.subscribeToStateChange(i,r)}isDraggingSource(i){return this.internalMonitor.isDraggingSource(i)}isOverTarget(i,r){return this.internalMonitor.isOverTarget(i,r)}getTargetIds(){return this.internalMonitor.getTargetIds()}isSourcePublic(){return this.internalMonitor.isSourcePublic()}getSourceId(){return this.internalMonitor.getSourceId()}subscribeToOffsetChange(i){return this.internalMonitor.subscribeToOffsetChange(i)}canDragSource(i){return this.internalMonitor.canDragSource(i)}canDropOnTarget(i){return this.internalMonitor.canDropOnTarget(i)}getItemType(){return this.internalMonitor.getItemType()}getItem(){return this.internalMonitor.getItem()}getDropResult(){return this.internalMonitor.getDropResult()}didDrop(){return this.internalMonitor.didDrop()}getInitialClientOffset(){return this.internalMonitor.getInitialClientOffset()}getInitialSourceClientOffset(){return this.internalMonitor.getInitialSourceClientOffset()}getSourceClientOffset(){return this.internalMonitor.getSourceClientOffset()}getClientOffset(){return this.internalMonitor.getClientOffset()}getDifferenceFromInitialOffset(){return this.internalMonitor.getDifferenceFromInitialOffset()}constructor(i){this.sourceId=null,this.internalMonitor=i.getMonitor()}}let Dr=!1;class x0{receiveHandlerId(i){this.targetId=i}getHandlerId(){return this.targetId}subscribeToStateChange(i,r){return this.internalMonitor.subscribeToStateChange(i,r)}canDrop(){if(!this.targetId)return!1;J(!Dr,"You may not call monitor.canDrop() inside your canDrop() implementation. Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target-monitor");try{return Dr=!0,this.internalMonitor.canDropOnTarget(this.targetId)}finally{Dr=!1}}isOver(i){return this.targetId?this.internalMonitor.isOverTarget(this.targetId,i):!1}getItemType(){return this.internalMonitor.getItemType()}getItem(){return this.internalMonitor.getItem()}getDropResult(){return this.internalMonitor.getDropResult()}didDrop(){return this.internalMonitor.didDrop()}getInitialClientOffset(){return this.internalMonitor.getInitialClientOffset()}getInitialSourceClientOffset(){return this.internalMonitor.getInitialSourceClientOffset()}getSourceClientOffset(){return this.internalMonitor.getSourceClientOffset()}getClientOffset(){return this.internalMonitor.getClientOffset()}getDifferenceFromInitialOffset(){return this.internalMonitor.getDifferenceFromInitialOffset()}constructor(i){this.targetId=null,this.internalMonitor=i.getMonitor()}}function z0(c,i,r){const s=r.getRegistry(),d=s.addTarget(c,i);return[d,()=>s.removeTarget(d)]}function N0(c,i,r){const s=r.getRegistry(),d=s.addSource(c,i);return[d,()=>s.removeSource(d)]}function _r(c,i,r,s){let d;if(d!==void 0)return!!d;if(c===i)return!0;if(typeof c!="object"||!c||typeof i!="object"||!i)return!1;const y=Object.keys(c),A=Object.keys(i);if(y.length!==A.length)return!1;const R=Object.prototype.hasOwnProperty.bind(i);for(let C=0;C<y.length;C++){const b=y[C];if(!R(b))return!1;const z=c[b],L=i[b];if(d=void 0,d===!1||d===void 0&&z!==L)return!1}return!0}function Rr(c){return c!==null&&typeof c=="object"&&Object.prototype.hasOwnProperty.call(c,"current")}function U0(c){if(typeof c.type=="string")return;const i=c.type.displayName||c.type.name||"the component";throw new Error(`Only native element nodes can now be passed to React DnD connectors.You can either wrap ${i} into a <div>, or turn it into a drag source or a drop target itself.`)}function H0(c){return(i=null,r=null)=>{if(!P.isValidElement(i)){const y=i;return c(y,r),y}const s=i;return U0(s),q0(s,r?y=>c(y,r):c)}}function vh(c){const i={};return Object.keys(c).forEach(r=>{const s=c[r];if(r.endsWith("Ref"))i[r]=c[r];else{const d=H0(s);i[r]=()=>d}}),i}function Pd(c,i){typeof c=="function"?c(i):c.current=i}function q0(c,i){const r=c.ref;return J(typeof r!="string","Cannot connect React DnD to an element with an existing string ref. Please convert it to use a callback ref instead, or wrap it into a <span> or <div>. Read more: https://reactjs.org/docs/refs-and-the-dom.html#callback-refs"),r?P.cloneElement(c,{ref:s=>{Pd(r,s),Pd(i,s)}}):P.cloneElement(c,{ref:i})}class j0{receiveHandlerId(i){this.handlerId!==i&&(this.handlerId=i,this.reconnect())}get connectTarget(){return this.dragSource}get dragSourceOptions(){return this.dragSourceOptionsInternal}set dragSourceOptions(i){this.dragSourceOptionsInternal=i}get dragPreviewOptions(){return this.dragPreviewOptionsInternal}set dragPreviewOptions(i){this.dragPreviewOptionsInternal=i}reconnect(){const i=this.reconnectDragSource();this.reconnectDragPreview(i)}reconnectDragSource(){const i=this.dragSource,r=this.didHandlerIdChange()||this.didConnectedDragSourceChange()||this.didDragSourceOptionsChange();return r&&this.disconnectDragSource(),this.handlerId?i?(r&&(this.lastConnectedHandlerId=this.handlerId,this.lastConnectedDragSource=i,this.lastConnectedDragSourceOptions=this.dragSourceOptions,this.dragSourceUnsubscribe=this.backend.connectDragSource(this.handlerId,i,this.dragSourceOptions)),r):(this.lastConnectedDragSource=i,r):r}reconnectDragPreview(i=!1){const r=this.dragPreview,s=i||this.didHandlerIdChange()||this.didConnectedDragPreviewChange()||this.didDragPreviewOptionsChange();if(s&&this.disconnectDragPreview(),!!this.handlerId){if(!r){this.lastConnectedDragPreview=r;return}s&&(this.lastConnectedHandlerId=this.handlerId,this.lastConnectedDragPreview=r,this.lastConnectedDragPreviewOptions=this.dragPreviewOptions,this.dragPreviewUnsubscribe=this.backend.connectDragPreview(this.handlerId,r,this.dragPreviewOptions))}}didHandlerIdChange(){return this.lastConnectedHandlerId!==this.handlerId}didConnectedDragSourceChange(){return this.lastConnectedDragSource!==this.dragSource}didConnectedDragPreviewChange(){return this.lastConnectedDragPreview!==this.dragPreview}didDragSourceOptionsChange(){return!_r(this.lastConnectedDragSourceOptions,this.dragSourceOptions)}didDragPreviewOptionsChange(){return!_r(this.lastConnectedDragPreviewOptions,this.dragPreviewOptions)}disconnectDragSource(){this.dragSourceUnsubscribe&&(this.dragSourceUnsubscribe(),this.dragSourceUnsubscribe=void 0)}disconnectDragPreview(){this.dragPreviewUnsubscribe&&(this.dragPreviewUnsubscribe(),this.dragPreviewUnsubscribe=void 0,this.dragPreviewNode=null,this.dragPreviewRef=null)}get dragSource(){return this.dragSourceNode||this.dragSourceRef&&this.dragSourceRef.current}get dragPreview(){return this.dragPreviewNode||this.dragPreviewRef&&this.dragPreviewRef.current}clearDragSource(){this.dragSourceNode=null,this.dragSourceRef=null}clearDragPreview(){this.dragPreviewNode=null,this.dragPreviewRef=null}constructor(i){this.hooks=vh({dragSource:(r,s)=>{this.clearDragSource(),this.dragSourceOptions=s||null,Rr(r)?this.dragSourceRef=r:this.dragSourceNode=r,this.reconnectDragSource()},dragPreview:(r,s)=>{this.clearDragPreview(),this.dragPreviewOptions=s||null,Rr(r)?this.dragPreviewRef=r:this.dragPreviewNode=r,this.reconnectDragPreview()}}),this.handlerId=null,this.dragSourceRef=null,this.dragSourceOptionsInternal=null,this.dragPreviewRef=null,this.dragPreviewOptionsInternal=null,this.lastConnectedHandlerId=null,this.lastConnectedDragSource=null,this.lastConnectedDragSourceOptions=null,this.lastConnectedDragPreview=null,this.lastConnectedDragPreviewOptions=null,this.backend=i}}class B0{get connectTarget(){return this.dropTarget}reconnect(){const i=this.didHandlerIdChange()||this.didDropTargetChange()||this.didOptionsChange();i&&this.disconnectDropTarget();const r=this.dropTarget;if(this.handlerId){if(!r){this.lastConnectedDropTarget=r;return}i&&(this.lastConnectedHandlerId=this.handlerId,this.lastConnectedDropTarget=r,this.lastConnectedDropTargetOptions=this.dropTargetOptions,this.unsubscribeDropTarget=this.backend.connectDropTarget(this.handlerId,r,this.dropTargetOptions))}}receiveHandlerId(i){i!==this.handlerId&&(this.handlerId=i,this.reconnect())}get dropTargetOptions(){return this.dropTargetOptionsInternal}set dropTargetOptions(i){this.dropTargetOptionsInternal=i}didHandlerIdChange(){return this.lastConnectedHandlerId!==this.handlerId}didDropTargetChange(){return this.lastConnectedDropTarget!==this.dropTarget}didOptionsChange(){return!_r(this.lastConnectedDropTargetOptions,this.dropTargetOptions)}disconnectDropTarget(){this.unsubscribeDropTarget&&(this.unsubscribeDropTarget(),this.unsubscribeDropTarget=void 0)}get dropTarget(){return this.dropTargetNode||this.dropTargetRef&&this.dropTargetRef.current}clearDropTarget(){this.dropTargetRef=null,this.dropTargetNode=null}constructor(i){this.hooks=vh({dropTarget:(r,s)=>{this.clearDropTarget(),this.dropTargetOptions=s,Rr(r)?this.dropTargetRef=r:this.dropTargetNode=r,this.reconnect()}}),this.handlerId=null,this.dropTargetRef=null,this.dropTargetOptionsInternal=null,this.lastConnectedHandlerId=null,this.lastConnectedDropTarget=null,this.lastConnectedDropTargetOptions=null,this.backend=i}}function Cn(){const{dragDropManager:c}=P.useContext(uh);return J(c!=null,"Expected drag drop context"),c}function Y0(c,i){const r=Cn(),s=P.useMemo(()=>new j0(r.getBackend()),[r]);return Yl(()=>(s.dragSourceOptions=c||null,s.reconnect(),()=>s.disconnectDragSource()),[s,c]),Yl(()=>(s.dragPreviewOptions=i||null,s.reconnect(),()=>s.disconnectDragPreview()),[s,i]),s}function G0(){const c=Cn();return P.useMemo(()=>new R0(c),[c])}class w0{beginDrag(){const i=this.spec,r=this.monitor;let s=null;return typeof i.item=="object"?s=i.item:typeof i.item=="function"?s=i.item(r):s={},s??null}canDrag(){const i=this.spec,r=this.monitor;return typeof i.canDrag=="boolean"?i.canDrag:typeof i.canDrag=="function"?i.canDrag(r):!0}isDragging(i,r){const s=this.spec,d=this.monitor,{isDragging:y}=s;return y?y(d):r===i.getSourceId()}endDrag(){const i=this.spec,r=this.monitor,s=this.connector,{end:d}=i;d&&d(r.getItem(),r),s.reconnect()}constructor(i,r,s){this.spec=i,this.monitor=r,this.connector=s}}function X0(c,i,r){const s=P.useMemo(()=>new w0(c,i,r),[i,r]);return P.useEffect(()=>{s.spec=c},[c]),s}function L0(c){return P.useMemo(()=>{const i=c.type;return J(i!=null,"spec.type must be defined"),i},[c])}function Q0(c,i,r){const s=Cn(),d=X0(c,i,r),y=L0(c);Yl(function(){if(y!=null){const[R,C]=N0(y,d,s);return i.receiveHandlerId(R),r.receiveHandlerId(R),C}},[s,i,r,d,y])}function yh(c,i){const r=gh(c,i);J(!r.begin,"useDrag::spec.begin was deprecated in v14. Replace spec.begin() with spec.item(). (see more here - https://react-dnd.github.io/react-dnd/docs/api/use-drag)");const s=G0(),d=Y0(r.options,r.previewOptions);return Q0(r,s,d),[hh(r.collect,s,d),C0(d),_0(d)]}function Z0(c){return P.useMemo(()=>c.hooks.dropTarget(),[c])}function V0(c){const i=Cn(),r=P.useMemo(()=>new B0(i.getBackend()),[i]);return Yl(()=>(r.dropTargetOptions=c||null,r.reconnect(),()=>r.disconnectDropTarget()),[c]),r}function K0(){const c=Cn();return P.useMemo(()=>new x0(c),[c])}function J0(c){const{accept:i}=c;return P.useMemo(()=>(J(c.accept!=null,"accept must be defined"),Array.isArray(i)?i:[i]),[i])}class k0{canDrop(){const i=this.spec,r=this.monitor;return i.canDrop?i.canDrop(r.getItem(),r):!0}hover(){const i=this.spec,r=this.monitor;i.hover&&i.hover(r.getItem(),r)}drop(){const i=this.spec,r=this.monitor;if(i.drop)return i.drop(r.getItem(),r)}constructor(i,r){this.spec=i,this.monitor=r}}function I0(c,i){const r=P.useMemo(()=>new k0(c,i),[i]);return P.useEffect(()=>{r.spec=c},[c]),r}function W0(c,i,r){const s=Cn(),d=I0(c,i),y=J0(c);Yl(function(){const[R,C]=z0(y,d,s);return i.receiveHandlerId(R),r.receiveHandlerId(R),C},[s,i,d,r,y.map(A=>A.toString()).join("|")])}function $0(c,i){const r=gh(c,i),s=K0(),d=V0(r.options);return W0(r,s,d),[hh(r.collect,s,d),Z0(d)]}function mh(c){let i=null;return()=>(i==null&&(i=c()),i)}function F0(c,i){return c.filter(r=>r!==i)}function P0(c,i){const r=new Set,s=y=>r.add(y);c.forEach(s),i.forEach(s);const d=[];return r.forEach(y=>d.push(y)),d}class tm{enter(i){const r=this.entered.length,s=d=>this.isNodeInDocument(d)&&(!d.contains||d.contains(i));return this.entered=P0(this.entered.filter(s),[i]),r===0&&this.entered.length>0}leave(i){const r=this.entered.length;return this.entered=F0(this.entered.filter(this.isNodeInDocument),i),r>0&&this.entered.length===0}reset(){this.entered=[]}constructor(i){this.entered=[],this.isNodeInDocument=i}}class em{initializeExposedProperties(){Object.keys(this.config.exposeProperties).forEach(i=>{Object.defineProperty(this.item,i,{configurable:!0,enumerable:!0,get(){return console.warn(`Browser doesn't allow reading "${i}" until the drop event.`),null}})})}loadDataTransfer(i){if(i){const r={};Object.keys(this.config.exposeProperties).forEach(s=>{const d=this.config.exposeProperties[s];d!=null&&(r[s]={value:d(i,this.config.matchesTypes),configurable:!0,enumerable:!0})}),Object.defineProperties(this.item,r)}}canDrag(){return!0}beginDrag(){return this.item}isDragging(i,r){return r===i.getSourceId()}endDrag(){}constructor(i){this.config=i,this.item={},this.initializeExposedProperties()}}const ph="__NATIVE_FILE__",Sh="__NATIVE_URL__",bh="__NATIVE_TEXT__",Th="__NATIVE_HTML__",th=Object.freeze(Object.defineProperty({__proto__:null,FILE:ph,HTML:Th,TEXT:bh,URL:Sh},Symbol.toStringTag,{value:"Module"}));function Or(c,i,r){const s=i.reduce((d,y)=>d||c.getData(y),"");return s??r}const xr={[ph]:{exposeProperties:{files:c=>Array.prototype.slice.call(c.files),items:c=>c.items,dataTransfer:c=>c},matchesTypes:["Files"]},[Th]:{exposeProperties:{html:(c,i)=>Or(c,i,""),dataTransfer:c=>c},matchesTypes:["Html","text/html"]},[Sh]:{exposeProperties:{urls:(c,i)=>Or(c,i,"").split(`
`),dataTransfer:c=>c},matchesTypes:["Url","text/uri-list"]},[bh]:{exposeProperties:{text:(c,i)=>Or(c,i,""),dataTransfer:c=>c},matchesTypes:["Text","text/plain"]}};function lm(c,i){const r=xr[c];if(!r)throw new Error(`native type ${c} has no configuration`);const s=new em(r);return s.loadDataTransfer(i),s}function Er(c){if(!c)return null;const i=Array.prototype.slice.call(c.types||[]);return Object.keys(xr).filter(r=>{const s=xr[r];return s!=null&&s.matchesTypes?s.matchesTypes.some(d=>i.indexOf(d)>-1):!1})[0]||null}const nm=mh(()=>/firefox/i.test(navigator.userAgent)),Dh=mh(()=>!!window.safari);class eh{interpolate(i){const{xs:r,ys:s,c1s:d,c2s:y,c3s:A}=this;let R=r.length-1;if(i===r[R])return s[R];let C=0,b=A.length-1,z;for(;C<=b;){z=Math.floor(.5*(C+b));const B=r[z];if(B<i)C=z+1;else if(B>i)b=z-1;else return s[z]}R=Math.max(0,b);const L=i-r[R],G=L*L;return s[R]+d[R]*L+y[R]*G+A[R]*L*G}constructor(i,r){const{length:s}=i,d=[];for(let B=0;B<s;B++)d.push(B);d.sort((B,nt)=>i[B]<i[nt]?-1:1);const y=[],A=[];let R,C;for(let B=0;B<s-1;B++)R=i[B+1]-i[B],C=r[B+1]-r[B],y.push(R),A.push(C/R);const b=[A[0]];for(let B=0;B<y.length-1;B++){const nt=A[B],k=A[B+1];if(nt*k<=0)b.push(0);else{R=y[B];const lt=y[B+1],yt=R+lt;b.push(3*yt/((yt+lt)/nt+(yt+R)/k))}}b.push(A[A.length-1]);const z=[],L=[];let G;for(let B=0;B<b.length-1;B++){G=A[B];const nt=b[B],k=1/y[B],lt=nt+b[B+1]-G-G;z.push((G-nt-lt)*k),L.push(lt*k*k)}this.xs=i,this.ys=r,this.c1s=b,this.c2s=z,this.c3s=L}}const am=1;function Oh(c){const i=c.nodeType===am?c:c.parentElement;if(!i)return null;const{top:r,left:s}=i.getBoundingClientRect();return{x:s,y:r}}function Vu(c){return{x:c.clientX,y:c.clientY}}function um(c){var i;return c.nodeName==="IMG"&&(nm()||!(!((i=document.documentElement)===null||i===void 0)&&i.contains(c)))}function im(c,i,r,s){let d=c?i.width:r,y=c?i.height:s;return Dh()&&c&&(y/=window.devicePixelRatio,d/=window.devicePixelRatio),{dragPreviewWidth:d,dragPreviewHeight:y}}function cm(c,i,r,s,d){const y=um(i),R=Oh(y?c:i),C={x:r.x-R.x,y:r.y-R.y},{offsetWidth:b,offsetHeight:z}=c,{anchorX:L,anchorY:G}=s,{dragPreviewWidth:B,dragPreviewHeight:nt}=im(y,i,b,z),k=()=>{let Mt=new eh([0,.5,1],[C.y,C.y/z*nt,C.y+nt-z]).interpolate(G);return Dh()&&y&&(Mt+=(window.devicePixelRatio-1)*nt),Mt},lt=()=>new eh([0,.5,1],[C.x,C.x/b*B,C.x+B-b]).interpolate(L),{offsetX:yt,offsetY:st}=d,mt=yt===0||yt,Yt=st===0||st;return{x:mt?yt:lt(),y:Yt?st:k()}}class rm{get window(){if(this.globalContext)return this.globalContext;if(typeof window<"u")return window}get document(){var i;return!((i=this.globalContext)===null||i===void 0)&&i.document?this.globalContext.document:this.window?this.window.document:void 0}get rootElement(){var i;return((i=this.optionsArgs)===null||i===void 0?void 0:i.rootElement)||this.window}constructor(i,r){this.ownerDocument=null,this.globalContext=i,this.optionsArgs=r}}function fm(c,i,r){return i in c?Object.defineProperty(c,i,{value:r,enumerable:!0,configurable:!0,writable:!0}):c[i]=r,c}function lh(c){for(var i=1;i<arguments.length;i++){var r=arguments[i]!=null?arguments[i]:{},s=Object.keys(r);typeof Object.getOwnPropertySymbols=="function"&&(s=s.concat(Object.getOwnPropertySymbols(r).filter(function(d){return Object.getOwnPropertyDescriptor(r,d).enumerable}))),s.forEach(function(d){fm(c,d,r[d])})}return c}class sm{profile(){var i,r;return{sourcePreviewNodes:this.sourcePreviewNodes.size,sourcePreviewNodeOptions:this.sourcePreviewNodeOptions.size,sourceNodeOptions:this.sourceNodeOptions.size,sourceNodes:this.sourceNodes.size,dragStartSourceIds:((i=this.dragStartSourceIds)===null||i===void 0?void 0:i.length)||0,dropTargetIds:this.dropTargetIds.length,dragEnterTargetIds:this.dragEnterTargetIds.length,dragOverTargetIds:((r=this.dragOverTargetIds)===null||r===void 0?void 0:r.length)||0}}get window(){return this.options.window}get document(){return this.options.document}get rootElement(){return this.options.rootElement}setup(){const i=this.rootElement;if(i!==void 0){if(i.__isReactDndBackendSetUp)throw new Error("Cannot have two HTML5 backends at the same time.");i.__isReactDndBackendSetUp=!0,this.addEventListeners(i)}}teardown(){const i=this.rootElement;if(i!==void 0&&(i.__isReactDndBackendSetUp=!1,this.removeEventListeners(this.rootElement),this.clearCurrentDragSourceNode(),this.asyncEndDragFrameId)){var r;(r=this.window)===null||r===void 0||r.cancelAnimationFrame(this.asyncEndDragFrameId)}}connectDragPreview(i,r,s){return this.sourcePreviewNodeOptions.set(i,s),this.sourcePreviewNodes.set(i,r),()=>{this.sourcePreviewNodes.delete(i),this.sourcePreviewNodeOptions.delete(i)}}connectDragSource(i,r,s){this.sourceNodes.set(i,r),this.sourceNodeOptions.set(i,s);const d=A=>this.handleDragStart(A,i),y=A=>this.handleSelectStart(A);return r.setAttribute("draggable","true"),r.addEventListener("dragstart",d),r.addEventListener("selectstart",y),()=>{this.sourceNodes.delete(i),this.sourceNodeOptions.delete(i),r.removeEventListener("dragstart",d),r.removeEventListener("selectstart",y),r.setAttribute("draggable","false")}}connectDropTarget(i,r){const s=A=>this.handleDragEnter(A,i),d=A=>this.handleDragOver(A,i),y=A=>this.handleDrop(A,i);return r.addEventListener("dragenter",s),r.addEventListener("dragover",d),r.addEventListener("drop",y),()=>{r.removeEventListener("dragenter",s),r.removeEventListener("dragover",d),r.removeEventListener("drop",y)}}addEventListeners(i){i.addEventListener&&(i.addEventListener("dragstart",this.handleTopDragStart),i.addEventListener("dragstart",this.handleTopDragStartCapture,!0),i.addEventListener("dragend",this.handleTopDragEndCapture,!0),i.addEventListener("dragenter",this.handleTopDragEnter),i.addEventListener("dragenter",this.handleTopDragEnterCapture,!0),i.addEventListener("dragleave",this.handleTopDragLeaveCapture,!0),i.addEventListener("dragover",this.handleTopDragOver),i.addEventListener("dragover",this.handleTopDragOverCapture,!0),i.addEventListener("drop",this.handleTopDrop),i.addEventListener("drop",this.handleTopDropCapture,!0))}removeEventListeners(i){i.removeEventListener&&(i.removeEventListener("dragstart",this.handleTopDragStart),i.removeEventListener("dragstart",this.handleTopDragStartCapture,!0),i.removeEventListener("dragend",this.handleTopDragEndCapture,!0),i.removeEventListener("dragenter",this.handleTopDragEnter),i.removeEventListener("dragenter",this.handleTopDragEnterCapture,!0),i.removeEventListener("dragleave",this.handleTopDragLeaveCapture,!0),i.removeEventListener("dragover",this.handleTopDragOver),i.removeEventListener("dragover",this.handleTopDragOverCapture,!0),i.removeEventListener("drop",this.handleTopDrop),i.removeEventListener("drop",this.handleTopDropCapture,!0))}getCurrentSourceNodeOptions(){const i=this.monitor.getSourceId(),r=this.sourceNodeOptions.get(i);return lh({dropEffect:this.altKeyPressed?"copy":"move"},r||{})}getCurrentDropEffect(){return this.isDraggingNativeItem()?"copy":this.getCurrentSourceNodeOptions().dropEffect}getCurrentSourcePreviewNodeOptions(){const i=this.monitor.getSourceId(),r=this.sourcePreviewNodeOptions.get(i);return lh({anchorX:.5,anchorY:.5,captureDraggingState:!1},r||{})}isDraggingNativeItem(){const i=this.monitor.getItemType();return Object.keys(th).some(r=>th[r]===i)}beginDragNativeItem(i,r){this.clearCurrentDragSourceNode(),this.currentNativeSource=lm(i,r),this.currentNativeHandle=this.registry.addSource(i,this.currentNativeSource),this.actions.beginDrag([this.currentNativeHandle])}setCurrentDragSourceNode(i){this.clearCurrentDragSourceNode(),this.currentDragSourceNode=i;const r=1e3;this.mouseMoveTimeoutTimer=setTimeout(()=>{var s;return(s=this.rootElement)===null||s===void 0?void 0:s.addEventListener("mousemove",this.endDragIfSourceWasRemovedFromDOM,!0)},r)}clearCurrentDragSourceNode(){if(this.currentDragSourceNode){if(this.currentDragSourceNode=null,this.rootElement){var i;(i=this.window)===null||i===void 0||i.clearTimeout(this.mouseMoveTimeoutTimer||void 0),this.rootElement.removeEventListener("mousemove",this.endDragIfSourceWasRemovedFromDOM,!0)}return this.mouseMoveTimeoutTimer=null,!0}return!1}handleDragStart(i,r){i.defaultPrevented||(this.dragStartSourceIds||(this.dragStartSourceIds=[]),this.dragStartSourceIds.unshift(r))}handleDragEnter(i,r){this.dragEnterTargetIds.unshift(r)}handleDragOver(i,r){this.dragOverTargetIds===null&&(this.dragOverTargetIds=[]),this.dragOverTargetIds.unshift(r)}handleDrop(i,r){this.dropTargetIds.unshift(r)}constructor(i,r,s){this.sourcePreviewNodes=new Map,this.sourcePreviewNodeOptions=new Map,this.sourceNodes=new Map,this.sourceNodeOptions=new Map,this.dragStartSourceIds=null,this.dropTargetIds=[],this.dragEnterTargetIds=[],this.currentNativeSource=null,this.currentNativeHandle=null,this.currentDragSourceNode=null,this.altKeyPressed=!1,this.mouseMoveTimeoutTimer=null,this.asyncEndDragFrameId=null,this.dragOverTargetIds=null,this.lastClientOffset=null,this.hoverRafId=null,this.getSourceClientOffset=d=>{const y=this.sourceNodes.get(d);return y&&Oh(y)||null},this.endDragNativeItem=()=>{this.isDraggingNativeItem()&&(this.actions.endDrag(),this.currentNativeHandle&&this.registry.removeSource(this.currentNativeHandle),this.currentNativeHandle=null,this.currentNativeSource=null)},this.isNodeInDocument=d=>!!(d&&this.document&&this.document.body&&this.document.body.contains(d)),this.endDragIfSourceWasRemovedFromDOM=()=>{const d=this.currentDragSourceNode;d==null||this.isNodeInDocument(d)||(this.clearCurrentDragSourceNode()&&this.monitor.isDragging()&&this.actions.endDrag(),this.cancelHover())},this.scheduleHover=d=>{this.hoverRafId===null&&typeof requestAnimationFrame<"u"&&(this.hoverRafId=requestAnimationFrame(()=>{this.monitor.isDragging()&&this.actions.hover(d||[],{clientOffset:this.lastClientOffset}),this.hoverRafId=null}))},this.cancelHover=()=>{this.hoverRafId!==null&&typeof cancelAnimationFrame<"u"&&(cancelAnimationFrame(this.hoverRafId),this.hoverRafId=null)},this.handleTopDragStartCapture=()=>{this.clearCurrentDragSourceNode(),this.dragStartSourceIds=[]},this.handleTopDragStart=d=>{if(d.defaultPrevented)return;const{dragStartSourceIds:y}=this;this.dragStartSourceIds=null;const A=Vu(d);this.monitor.isDragging()&&(this.actions.endDrag(),this.cancelHover()),this.actions.beginDrag(y||[],{publishSource:!1,getSourceClientOffset:this.getSourceClientOffset,clientOffset:A});const{dataTransfer:R}=d,C=Er(R);if(this.monitor.isDragging()){if(R&&typeof R.setDragImage=="function"){const z=this.monitor.getSourceId(),L=this.sourceNodes.get(z),G=this.sourcePreviewNodes.get(z)||L;if(G){const{anchorX:B,anchorY:nt,offsetX:k,offsetY:lt}=this.getCurrentSourcePreviewNodeOptions(),mt=cm(L,G,A,{anchorX:B,anchorY:nt},{offsetX:k,offsetY:lt});R.setDragImage(G,mt.x,mt.y)}}try{R==null||R.setData("application/json",{})}catch{}this.setCurrentDragSourceNode(d.target);const{captureDraggingState:b}=this.getCurrentSourcePreviewNodeOptions();b?this.actions.publishDragSource():setTimeout(()=>this.actions.publishDragSource(),0)}else if(C)this.beginDragNativeItem(C);else{if(R&&!R.types&&(d.target&&!d.target.hasAttribute||!d.target.hasAttribute("draggable")))return;d.preventDefault()}},this.handleTopDragEndCapture=()=>{this.clearCurrentDragSourceNode()&&this.monitor.isDragging()&&this.actions.endDrag(),this.cancelHover()},this.handleTopDragEnterCapture=d=>{if(this.dragEnterTargetIds=[],this.isDraggingNativeItem()){var y;(y=this.currentNativeSource)===null||y===void 0||y.loadDataTransfer(d.dataTransfer)}if(!this.enterLeaveCounter.enter(d.target)||this.monitor.isDragging())return;const{dataTransfer:R}=d,C=Er(R);C&&this.beginDragNativeItem(C,R)},this.handleTopDragEnter=d=>{const{dragEnterTargetIds:y}=this;if(this.dragEnterTargetIds=[],!this.monitor.isDragging())return;this.altKeyPressed=d.altKey,y.length>0&&this.actions.hover(y,{clientOffset:Vu(d)}),y.some(R=>this.monitor.canDropOnTarget(R))&&(d.preventDefault(),d.dataTransfer&&(d.dataTransfer.dropEffect=this.getCurrentDropEffect()))},this.handleTopDragOverCapture=d=>{if(this.dragOverTargetIds=[],this.isDraggingNativeItem()){var y;(y=this.currentNativeSource)===null||y===void 0||y.loadDataTransfer(d.dataTransfer)}},this.handleTopDragOver=d=>{const{dragOverTargetIds:y}=this;if(this.dragOverTargetIds=[],!this.monitor.isDragging()){d.preventDefault(),d.dataTransfer&&(d.dataTransfer.dropEffect="none");return}this.altKeyPressed=d.altKey,this.lastClientOffset=Vu(d),this.scheduleHover(y),(y||[]).some(R=>this.monitor.canDropOnTarget(R))?(d.preventDefault(),d.dataTransfer&&(d.dataTransfer.dropEffect=this.getCurrentDropEffect())):this.isDraggingNativeItem()?d.preventDefault():(d.preventDefault(),d.dataTransfer&&(d.dataTransfer.dropEffect="none"))},this.handleTopDragLeaveCapture=d=>{this.isDraggingNativeItem()&&d.preventDefault(),this.enterLeaveCounter.leave(d.target)&&(this.isDraggingNativeItem()&&setTimeout(()=>this.endDragNativeItem(),0),this.cancelHover())},this.handleTopDropCapture=d=>{if(this.dropTargetIds=[],this.isDraggingNativeItem()){var y;d.preventDefault(),(y=this.currentNativeSource)===null||y===void 0||y.loadDataTransfer(d.dataTransfer)}else Er(d.dataTransfer)&&d.preventDefault();this.enterLeaveCounter.reset()},this.handleTopDrop=d=>{const{dropTargetIds:y}=this;this.dropTargetIds=[],this.actions.hover(y,{clientOffset:Vu(d)}),this.actions.drop({dropEffect:this.getCurrentDropEffect()}),this.isDraggingNativeItem()?this.endDragNativeItem():this.monitor.isDragging()&&this.actions.endDrag(),this.cancelHover()},this.handleSelectStart=d=>{const y=d.target;typeof y.dragDrop=="function"&&(y.tagName==="INPUT"||y.tagName==="SELECT"||y.tagName==="TEXTAREA"||y.isContentEditable||(d.preventDefault(),y.dragDrop()))},this.options=new rm(r,s),this.actions=i.getActions(),this.monitor=i.getMonitor(),this.registry=i.getRegistry(),this.enterLeaveCounter=new tm(this.isNodeInDocument)}}const om=function(i,r,s){return new sm(i,r,s)},Yr={COMPONENT:"component"},dm={padding:"8px 12px",margin:"5px 0",backgroundColor:"#333",border:"1px solid #555",borderRadius:"4px",cursor:"grab",color:"#90e0ef",textAlign:"center"},Ca=({type:c})=>{const[{isDragging:i},r]=yh(()=>({type:Yr.COMPONENT,item:{type:c},collect:s=>({isDragging:!!s.isDragging()})}));return H.jsx("div",{ref:r,style:{...dm,opacity:i?.5:1},children:c})};function hm(){return H.jsxs("div",{children:[H.jsx("h4",{style:{color:"#ade8f4",borderBottom:"1px solid #444",paddingBottom:"5px"},children:"Components"}),H.jsx(Ca,{type:"TextBlock"}),H.jsx(Ca,{type:"Image"}),H.jsx(Ca,{type:"Button"}),H.jsx(Ca,{type:"ProductList"}),H.jsx(Ca,{type:"BookingForm"})]})}const gm=({component:c})=>{const{selectComponent:i,selectedComponentId:r,updateComponentProps:s}=P.useContext(Ra),d=P.useRef(null),y=c.id===r,[{isDragging:A},R]=yh(()=>({type:Yr.COMPONENT,item:{id:c.id,type:c.type,originalIndex:-1},collect:z=>({isDragging:z.isDragging()})}),[c.id,c.type]);R(d);const C={...c.props.style,border:y?"2px solid #00b4d8":"1px solid #555",padding:"10px",backgroundColor:"#444",cursor:A?"grabbing":"grab",minWidth:"50px",minHeight:"30px",opacity:A?.4:1},b=z=>{z.stopPropagation(),i(c.id)};switch(c.type){case"TextBlock":return H.jsx("div",{ref:d,style:C,onClick:b,children:c.props.text||"Text Block"});case"Image":return H.jsx("img",{ref:d,src:c.props.src||"https://via.placeholder.com/150",alt:"placeholder",style:{...C,padding:0},onClick:b});case"Button":return H.jsx("button",{ref:d,style:C,onClick:b,children:c.props.label||"Button"});default:return H.jsx("div",{ref:d,style:C,onClick:b,children:"Unknown Component"})}},vm={border:"1px dashed #555",minHeight:"calc(100vh - 40px)",backgroundColor:"#222",position:"relative"};function ym(){const{components:c,addComponent:i,selectComponent:r,updateComponentProps:s}=P.useContext(Ra),[,d]=$0(()=>({accept:Yr.COMPONENT,drop:(A,R)=>{const C=R.getClientOffset(),b=R.getTargetIds()[0]?document.getElementById("editor-canvas").getBoundingClientRect():{left:0,top:0};let z=Math.round(C.x-b.left),L=Math.round(C.y-b.top);A.id?s(A.id,{style:{...c.find(G=>G.id===A.id).props.style,left:z,top:L}}):i(A.type,{x:z,y:L})}}),[i,s,c]),y=()=>{r(null)};return H.jsxs("div",{ref:d,style:vm,id:"editor-canvas",onClick:y,children:[c.length===0&&H.jsx("p",{style:{color:"#888",textAlign:"center",paddingTop:"50px"},children:"Drag components here"}),c.map(A=>H.jsx(gm,{component:A},A.id))]})}const Ar={marginBottom:"15px"},Ke={display:"block",marginBottom:"5px",color:"#ade8f4",fontSize:"0.9em"},pe={width:"100%",padding:"8px",border:"1px solid #555",backgroundColor:"#333",color:"#eee",borderRadius:"4px",boxSizing:"border-box"};function mm(){var L,G,B,nt,k,lt;const{components:c,selectedComponentId:i,updateComponentProps:r,deleteComponent:s}=P.useContext(Ra),[d,y]=P.useState(null),[A,R]=P.useState(!1),C=c.find(yt=>yt.id===i);P.useEffect(()=>{y(C?C.props:null)},[i,c]);const b=yt=>{const{name:st,value:mt,type:Yt}=yt.target;let $=mt;if(st.startsWith("style.")){const Mt=st.split(".")[1],Gt={...d.style,[Mt]:$};y(Vt=>({...Vt,style:Gt})),r(i,{style:Gt})}else y(Mt=>({...Mt,[st]:$})),r(i,{[st]:$})};if(!C||!d)return H.jsxs("div",{children:[H.jsx("h4",{style:{color:"#ade8f4",borderBottom:"1px solid #444",paddingBottom:"5px"},children:"Properties"}),H.jsx("p",{style:{color:"#888",textAlign:"center",paddingTop:"20px"},children:"Select an element on the canvas to edit its properties."})]});const z={...pe,backgroundColor:"#ff4d4f",color:"white",border:"none",marginTop:"15px",cursor:"pointer",":hover":{backgroundColor:"#ff7875"}};return H.jsxs("div",{children:[H.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[H.jsxs("h4",{style:{color:"#ade8f4",borderBottom:"1px solid #444",paddingBottom:"5px"},children:["Properties (",C.type,")"]}),H.jsx("button",{style:z,onClick:()=>s(i),children:"Delete Component"})]}),C.type==="TextBlock"&&H.jsxs("div",{style:Ar,children:[H.jsx("label",{style:Ke,htmlFor:"text",children:"Content:"}),H.jsx("textarea",{id:"text",name:"text",style:{...pe,height:"80px"},value:d.text||"",onChange:b}),H.jsx("label",{style:Ke,htmlFor:"style.color",children:"Color:"}),H.jsx("input",{id:"style.color",name:"style.color",type:"color",style:pe,value:((L=d.style)==null?void 0:L.color)||"#FFFFFF",onChange:b}),H.jsx("label",{style:Ke,htmlFor:"style.fontSize",children:"Font Size (px):"}),H.jsx("input",{id:"style.fontSize",name:"style.fontSize",type:"number",style:pe,value:((G=d.style)==null?void 0:G.fontSize)||16,onChange:b})]}),C.type==="Image"&&H.jsxs("div",{style:Ar,children:[H.jsx("label",{style:Ke,htmlFor:"src",children:"Image URL:"}),H.jsx("input",{id:"src",name:"src",type:"text",style:pe,value:d.src||"",onChange:b}),H.jsx("label",{style:Ke,htmlFor:"style.width",children:"Width (px):"}),H.jsx("input",{id:"style.width",name:"style.width",type:"number",style:pe,value:((B=d.style)==null?void 0:B.width)||"",onChange:b}),H.jsx("label",{style:Ke,htmlFor:"style.height",children:"Height (px):"}),H.jsx("input",{id:"style.height",name:"style.height",type:"number",style:pe,value:((nt=d.style)==null?void 0:nt.height)||"",onChange:b})]}),C.type==="Button"&&H.jsxs("div",{style:Ar,children:[H.jsx("label",{style:Ke,htmlFor:"label",children:"Button Text:"}),H.jsx("input",{id:"label",name:"label",type:"text",style:pe,value:d.label||"",onChange:b}),H.jsx("label",{style:Ke,htmlFor:"style.backgroundColor",children:"Background Color:"}),H.jsx("input",{id:"style.backgroundColor",name:"style.backgroundColor",type:"color",style:pe,value:((k=d.style)==null?void 0:k.backgroundColor)||"#007bff",onChange:b}),H.jsx("label",{style:Ke,htmlFor:"style.color",children:"Text Color:"}),H.jsx("input",{id:"style.color",name:"style.color",type:"color",style:pe,value:((lt=d.style)==null?void 0:lt.color)||"#FFFFFF",onChange:b})]}),A&&H.jsx("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0,0,0,0.7)",display:"flex",justifyContent:"center",alignItems:"center",zIndex:1e3},children:H.jsxs("div",{style:{backgroundColor:"#333",padding:"20px",borderRadius:"8px",maxWidth:"400px"},children:[H.jsx("h4",{style:{color:"#ade8f4"},children:"Confirm Delete"}),H.jsx("p",{style:{color:"#eee"},children:"Are you sure you want to delete this component?"}),H.jsxs("div",{style:{display:"flex",justifyContent:"flex-end",gap:"10px",marginTop:"20px"},children:[H.jsx("button",{style:{...pe,backgroundColor:"#444",color:"#eee"},onClick:()=>R(!1),children:"Cancel"}),H.jsx("button",{style:{...pe,backgroundColor:"#ff4d4f",color:"white"},onClick:()=>{s(i),R(!1)},children:"Delete"})]})]})})]})}const pm={display:"flex",height:"100vh",backgroundColor:"#111",color:"#eee"},Sm={width:"200px",borderRight:"1px solid #444",padding:"10px",overflowY:"auto"},bm={flexGrow:1,padding:"20px",overflowY:"auto"},Tm={width:"250px",borderLeft:"1px solid #444",padding:"10px",overflowY:"auto"},Dm={display:"flex",gap:"10px",padding:"10px",backgroundColor:"#222",borderBottom:"1px solid #444"},nh={padding:"8px 12px",backgroundColor:"#333",border:"1px solid #555",borderRadius:"4px",color:"#eee",cursor:"pointer",":hover":{backgroundColor:"#444"}};function Om(){const{undo:c,redo:i,canUndo:r,canRedo:s}=P.useContext(Ra);return P.useEffect(()=>{const d=y=>{(y.ctrlKey||y.metaKey)&&y.key==="z"&&(y.shiftKey&&s?i():r&&c())};return window.addEventListener("keydown",d),()=>{window.removeEventListener("keydown",d)}},[c,i,r,s]),H.jsxs("div",{style:pm,children:[H.jsxs("div",{style:Dm,children:[H.jsx("button",{style:{...nh,opacity:r?1:.5,cursor:r?"pointer":"not-allowed"},onClick:c,disabled:!r,children:"Undo"}),H.jsx("button",{style:{...nh,opacity:s?1:.5,cursor:s?"pointer":"not-allowed"},onClick:i,disabled:!s,children:"Redo"})]}),H.jsx("div",{style:Sm,children:H.jsx(hm,{})}),H.jsx("div",{style:bm,children:H.jsx(ym,{})}),H.jsx("div",{style:Tm,children:H.jsx(mm,{})})]})}function Em(){return H.jsx(S0,{backend:om,children:H.jsx(ah,{children:H.jsx(Om,{})})})}function Am(){return H.jsx("div",{className:"app-container",children:H.jsx(ah,{children:H.jsx(Em,{})})})}ly.createRoot(document.getElementById("root")).render(H.jsx(P.StrictMode,{children:H.jsx(Am,{})}));
