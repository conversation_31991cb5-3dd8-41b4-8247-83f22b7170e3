import React from 'react';
import { Link } from 'react-router-dom';
import Header from '../components/common/Header';
import Footer from '../components/common/Footer';

function SuccessPage() {
  return (
    <div className="success-page">
      <Header />
      <main className="success-content">
        <div className="container">
          <div className="success-card">
            <div className="success-icon">
              <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="12" cy="12" r="10" stroke="#10B981" strokeWidth="2" fill="#F0FDF4"/>
                <path d="m9 12 2 2 4-4" stroke="#10B981" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
            
            <h1>Registration Successful!</h1>
            
            <p className="success-message">
              Thank you for registering with Connect+ Apps. Your account has been created successfully.
            </p>
            
            <div className="next-steps">
              <h3>What happens next?</h3>
              <ul>
                <li>Our team will review your registration within 24 hours</li>
                <li>You'll receive a welcome email with setup instructions</li>
                <li>A dedicated account manager will contact you to schedule onboarding</li>
                <li>We'll help you configure your Connect+ Apps system for your business needs</li>
              </ul>
            </div>
            
            <div className="contact-info">
              <h3>Need immediate assistance?</h3>
              <p>Contact our support team:</p>
              <p>
                <strong>Email:</strong> <EMAIL><br/>
                <strong>Phone:</strong> 1-800-CONNECT
              </p>
            </div>
            
            <div className="action-buttons">
              <Link to="/" className="btn btn-primary">
                Return to Home
              </Link>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}

export default SuccessPage;
