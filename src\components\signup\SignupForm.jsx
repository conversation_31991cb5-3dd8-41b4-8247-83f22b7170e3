import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { submitRegistration } from '../../services/authService';

const styles = {
  form: {
    maxWidth: '600px',
    margin: '0 auto',
    padding: '2rem',
    backgroundColor: '#fff',
    borderRadius: '8px',
    boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
  },
  title: {
    textAlign: 'center',
    marginBottom: '2rem',
    color: '#333'
  },
  step: {
    marginBottom: '2rem'
  },
  stepTitle: {
    fontSize: '1.2rem',
    fontWeight: 'bold',
    marginBottom: '1rem',
    color: '#007bff',
    borderBottom: '2px solid #007bff',
    paddingBottom: '0.5rem'
  },
  formGroup: {
    marginBottom: '1rem'
  },
  label: {
    display: 'block',
    marginBottom: '0.5rem',
    fontWeight: 'bold',
    color: '#333'
  },
  required: {
    color: '#dc3545'
  },
  input: {
    width: '100%',
    padding: '0.75rem',
    border: '1px solid #ddd',
    borderRadius: '4px',
    fontSize: '1rem',
    boxSizing: 'border-box'
  },
  textarea: {
    width: '100%',
    padding: '0.75rem',
    border: '1px solid #ddd',
    borderRadius: '4px',
    fontSize: '1rem',
    minHeight: '100px',
    resize: 'vertical',
    boxSizing: 'border-box'
  },
  select: {
    width: '100%',
    padding: '0.75rem',
    border: '1px solid #ddd',
    borderRadius: '4px',
    fontSize: '1rem',
    backgroundColor: '#fff',
    boxSizing: 'border-box'
  },
  checkbox: {
    marginRight: '0.5rem'
  },
  checkboxLabel: {
    display: 'flex',
    alignItems: 'flex-start',
    gap: '0.5rem',
    fontSize: '0.9rem',
    lineHeight: '1.4'
  },
  error: {
    color: '#dc3545',
    fontSize: '0.875rem',
    marginTop: '0.25rem'
  },
  button: {
    backgroundColor: '#007bff',
    color: 'white',
    padding: '1rem 2rem',
    border: 'none',
    borderRadius: '4px',
    fontSize: '1rem',
    cursor: 'pointer',
    width: '100%',
    marginTop: '1rem'
  },
  buttonDisabled: {
    backgroundColor: '#6c757d',
    cursor: 'not-allowed'
  },
  loading: {
    textAlign: 'center',
    padding: '2rem'
  },
  progressBar: {
    width: '100%',
    height: '4px',
    backgroundColor: '#e9ecef',
    borderRadius: '2px',
    marginBottom: '2rem',
    overflow: 'hidden'
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#007bff',
    transition: 'width 0.3s ease'
  }
};

function SignupForm() {
  const { token } = useParams();
  const navigate = useNavigate();

  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState({});

  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    companyName: '',
    businessType: '',
    businessSize: '',
    currentPosSystem: '',
    specificNeeds: '',
    preferredContactMethod: 'email',
    marketingConsent: false,
    termsAccepted: false
  });

  const totalSteps = 3;
  const progressPercentage = (currentStep / totalSteps) * 100;

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateStep = (step) => {
    const newErrors = {};

    if (step === 1) {
      if (!formData.firstName.trim()) newErrors.firstName = 'First name is required';
      if (!formData.lastName.trim()) newErrors.lastName = 'Last name is required';
      if (!formData.email.trim()) {
        newErrors.email = 'Email is required';
      } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
        newErrors.email = 'Please enter a valid email address';
      }
      if (!formData.phone.trim()) newErrors.phone = 'Phone number is required';
    }

    if (step === 2) {
      if (!formData.companyName.trim()) newErrors.companyName = 'Company name is required';
      if (!formData.businessType) newErrors.businessType = 'Please select your business type';
      if (!formData.businessSize) newErrors.businessSize = 'Please select your business size';
    }

    if (step === 3) {
      if (!formData.termsAccepted) newErrors.termsAccepted = 'You must accept the terms and conditions';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, totalSteps));
    }
  };

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateStep(currentStep)) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Get user's IP and user agent for security logging
      const userAgent = navigator.userAgent;

      const result = await submitRegistration(formData, token, null, userAgent);

      if (result.success) {
        navigate('/success');
      } else {
        setErrors({ submit: result.error || 'Registration failed. Please try again.' });
      }
    } catch (error) {
      console.error('Registration error:', error);
      setErrors({ submit: 'An unexpected error occurred. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isSubmitting) {
    return (
      <div style={styles.loading}>
        <h2>Submitting your registration...</h2>
        <p>Please wait while we process your information.</p>
      </div>
    );
  }

  return (
    <form style={styles.form} onSubmit={handleSubmit}>
      <h1 style={styles.title}>Connect+ Apps Registration</h1>

      <div style={styles.progressBar}>
        <div style={{...styles.progressFill, width: `${progressPercentage}%`}}></div>
      </div>

      <p style={{ textAlign: 'center', marginBottom: '2rem', color: '#666' }}>
        Step {currentStep} of {totalSteps}
      </p>

      {/* Step 1: Personal Information */}
      {currentStep === 1 && (
        <div style={styles.step}>
          <h2 style={styles.stepTitle}>Personal Information</h2>

          <div style={styles.formGroup}>
            <label style={styles.label}>
              First Name <span style={styles.required}>*</span>
            </label>
            <input
              type="text"
              name="firstName"
              value={formData.firstName}
              onChange={handleInputChange}
              style={styles.input}
              required
            />
            {errors.firstName && <div style={styles.error}>{errors.firstName}</div>}
          </div>

          <div style={styles.formGroup}>
            <label style={styles.label}>
              Last Name <span style={styles.required}>*</span>
            </label>
            <input
              type="text"
              name="lastName"
              value={formData.lastName}
              onChange={handleInputChange}
              style={styles.input}
              required
            />
            {errors.lastName && <div style={styles.error}>{errors.lastName}</div>}
          </div>

          <div style={styles.formGroup}>
            <label style={styles.label}>
              Email Address <span style={styles.required}>*</span>
            </label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              style={styles.input}
              required
            />
            {errors.email && <div style={styles.error}>{errors.email}</div>}
          </div>

          <div style={styles.formGroup}>
            <label style={styles.label}>
              Phone Number <span style={styles.required}>*</span>
            </label>
            <input
              type="tel"
              name="phone"
              value={formData.phone}
              onChange={handleInputChange}
              style={styles.input}
              required
            />
            {errors.phone && <div style={styles.error}>{errors.phone}</div>}
          </div>
        </div>
      )}

      {/* Step 2: Business Information */}
      {currentStep === 2 && (
        <div style={styles.step}>
          <h2 style={styles.stepTitle}>Business Information</h2>

          <div style={styles.formGroup}>
            <label style={styles.label}>
              Company Name <span style={styles.required}>*</span>
            </label>
            <input
              type="text"
              name="companyName"
              value={formData.companyName}
              onChange={handleInputChange}
              style={styles.input}
              required
            />
            {errors.companyName && <div style={styles.error}>{errors.companyName}</div>}
          </div>

          <div style={styles.formGroup}>
            <label style={styles.label}>
              Business Type <span style={styles.required}>*</span>
            </label>
            <select
              name="businessType"
              value={formData.businessType}
              onChange={handleInputChange}
              style={styles.select}
              required
            >
              <option value="">Select your business type</option>
              <option value="retail">Retail Store</option>
              <option value="restaurant">Restaurant/Food Service</option>
              <option value="salon">Salon/Spa</option>
              <option value="automotive">Automotive</option>
              <option value="healthcare">Healthcare</option>
              <option value="professional">Professional Services</option>
              <option value="other">Other</option>
            </select>
            {errors.businessType && <div style={styles.error}>{errors.businessType}</div>}
          </div>

          <div style={styles.formGroup}>
            <label style={styles.label}>
              Business Size <span style={styles.required}>*</span>
            </label>
            <select
              name="businessSize"
              value={formData.businessSize}
              onChange={handleInputChange}
              style={styles.select}
              required
            >
              <option value="">Select your business size</option>
              <option value="single">Single Location</option>
              <option value="2-5">2-5 Locations</option>
              <option value="6-20">6-20 Locations</option>
              <option value="21+">21+ Locations</option>
            </select>
            {errors.businessSize && <div style={styles.error}>{errors.businessSize}</div>}
          </div>

          <div style={styles.formGroup}>
            <label style={styles.label}>
              Current POS System (if any)
            </label>
            <input
              type="text"
              name="currentPosSystem"
              value={formData.currentPosSystem}
              onChange={handleInputChange}
              style={styles.input}
              placeholder="e.g., Square, Clover, Toast, or None"
            />
          </div>

          <div style={styles.formGroup}>
            <label style={styles.label}>
              Specific Needs or Requirements
            </label>
            <textarea
              name="specificNeeds"
              value={formData.specificNeeds}
              onChange={handleInputChange}
              style={styles.textarea}
              placeholder="Tell us about your specific business needs, challenges, or requirements..."
            />
          </div>

          <div style={styles.formGroup}>
            <label style={styles.label}>
              Preferred Contact Method
            </label>
            <select
              name="preferredContactMethod"
              value={formData.preferredContactMethod}
              onChange={handleInputChange}
              style={styles.select}
            >
              <option value="email">Email</option>
              <option value="phone">Phone</option>
              <option value="both">Both Email and Phone</option>
            </select>
          </div>
        </div>
      )}

      {/* Step 3: Terms and Preferences */}
      {currentStep === 3 && (
        <div style={styles.step}>
          <h2 style={styles.stepTitle}>Terms and Preferences</h2>

          <div style={styles.formGroup}>
            <label style={styles.checkboxLabel}>
              <input
                type="checkbox"
                name="marketingConsent"
                checked={formData.marketingConsent}
                onChange={handleInputChange}
                style={styles.checkbox}
              />
              <span>
                I would like to receive marketing communications about Connect+ Apps products,
                services, and special offers. You can unsubscribe at any time.
              </span>
            </label>
          </div>

          <div style={styles.formGroup}>
            <label style={styles.checkboxLabel}>
              <input
                type="checkbox"
                name="termsAccepted"
                checked={formData.termsAccepted}
                onChange={handleInputChange}
                style={styles.checkbox}
                required
              />
              <span>
                I have read and agree to the{' '}
                <a href="/terms" target="_blank" rel="noopener noreferrer" style={{color: '#007bff'}}>
                  Terms and Conditions of Service
                </a>{' '}
                <span style={styles.required}>*</span>
              </span>
            </label>
            {errors.termsAccepted && <div style={styles.error}>{errors.termsAccepted}</div>}
          </div>

          <div style={{
            backgroundColor: '#f8f9fa',
            border: '1px solid #dee2e6',
            borderRadius: '6px',
            padding: '1rem',
            marginTop: '1.5rem',
            fontSize: '0.9rem',
            color: '#6c757d'
          }}>
            <h4 style={{marginTop: 0, color: '#495057'}}>What happens next?</h4>
            <ul style={{marginBottom: 0, paddingLeft: '1.2rem'}}>
              <li>Our team will review your registration within 24 hours</li>
              <li>You'll receive a welcome email with next steps</li>
              <li>A dedicated account manager will contact you to schedule onboarding</li>
              <li>We'll help configure Connect+ Apps for your specific business needs</li>
            </ul>
          </div>
        </div>
      )}

      {/* Navigation buttons */}
      <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: '2rem' }}>
        {currentStep > 1 && (
          <button
            type="button"
            onClick={handlePrevious}
            style={{...styles.button, backgroundColor: '#6c757d', width: 'auto', padding: '0.75rem 1.5rem'}}
          >
            Previous
          </button>
        )}

        {currentStep < totalSteps ? (
          <button
            type="button"
            onClick={handleNext}
            style={{...styles.button, width: 'auto', padding: '0.75rem 1.5rem', marginLeft: 'auto'}}
          >
            Next
          </button>
        ) : (
          <button
            type="submit"
            disabled={isSubmitting}
            style={{
              ...styles.button,
              ...(isSubmitting ? styles.buttonDisabled : {}),
              width: 'auto',
              padding: '0.75rem 1.5rem',
              marginLeft: 'auto'
            }}
          >
            {isSubmitting ? 'Submitting...' : 'Complete Registration'}
          </button>
        )}
      </div>

      {errors.submit && (
        <div style={{...styles.error, textAlign: 'center', marginTop: '1rem'}}>
          {errors.submit}
        </div>
      )}
    </form>
  );
}

export default SignupForm;
