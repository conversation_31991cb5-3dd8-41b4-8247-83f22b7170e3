import { supabase } from '../supabaseClient';
import { generateSecureToken, calculateTokenExpiration, createSignupUrl } from '../utils/tokenUtils';

/**
 * Sends a signup invitation email with a secure token
 * @param {string} email - Recipient email address
 * @param {string} senderName - Name of the person sending the invitation
 * @param {string} customMessage - Optional custom message
 * @returns {Promise<{success: boolean, token: string|null, error: string|null}>}
 */
export async function sendSignupInvitation(email, senderName = 'Connect+ Apps Team', customMessage = '') {
  try {
    if (!email || !email.includes('@')) {
      return {
        success: false,
        token: null,
        error: 'Valid email address is required'
      };
    }

    if (!supabase) {
      return {
        success: false,
        token: null,
        error: 'Service temporarily unavailable'
      };
    }

    // Generate secure token
    const token = generateSecureToken(32);
    const expiresAt = calculateTokenExpiration(24); // 24 hours from now

    // Store token in database
    const { data: tokenData, error: tokenError } = await supabase
      .from('signup_tokens')
      .insert({
        token: token,
        email: email,
        expires_at: expiresAt.toISOString(),
        created_by: senderName,
        metadata: {
          custom_message: customMessage,
          sent_at: new Date().toISOString()
        }
      })
      .select('id')
      .single();

    if (tokenError) {
      console.error('Token creation error:', tokenError);
      return {
        success: false,
        token: null,
        error: 'Failed to create signup token'
      };
    }

    // Create signup URL
    const signupUrl = createSignupUrl(token);

    // Prepare email content
    const emailContent = createEmailTemplate(email, signupUrl, senderName, customMessage, expiresAt);

    // In a production environment, you would integrate with an email service here
    // For now, we'll log the email content and return success
    console.log('Email would be sent with the following content:');
    console.log('To:', email);
    console.log('Subject:', emailContent.subject);
    console.log('Body:', emailContent.body);
    console.log('Signup URL:', signupUrl);

    // TODO: Integrate with actual email service (Resend, SendGrid, etc.)
    // Example with Resend:
    // const emailResult = await sendEmailWithResend(email, emailContent.subject, emailContent.body);

    return {
      success: true,
      token: token,
      error: null
    };

  } catch (error) {
    console.error('Send invitation error:', error);
    return {
      success: false,
      token: null,
      error: 'Failed to send invitation'
    };
  }
}

/**
 * Creates email template for signup invitation
 * @param {string} email - Recipient email
 * @param {string} signupUrl - Signup URL with token
 * @param {string} senderName - Sender name
 * @param {string} customMessage - Custom message
 * @param {Date} expiresAt - Token expiration date
 * @returns {object} Email template with subject and body
 */
function createEmailTemplate(email, signupUrl, senderName, customMessage, expiresAt) {
  const expirationTime = expiresAt.toLocaleString();
  
  const subject = 'Your Connect+ Apps Registration Invitation';
  
  const body = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connect+ Apps Registration Invitation</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #007bff; color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background: #f8f9fa; padding: 30px; border-radius: 0 0 8px 8px; }
        .button { display: inline-block; background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; margin: 20px 0; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Connect+ Apps</h1>
            <p>You're Invited to Join Our Platform</p>
        </div>
        
        <div class="content">
            <h2>Hello!</h2>
            
            <p>You've been invited by <strong>${senderName}</strong> to register for Connect+ Apps, our integrated business management platform.</p>
            
            ${customMessage ? `<div class="warning"><strong>Personal Message:</strong><br>${customMessage}</div>` : ''}
            
            <p>Connect+ Apps provides comprehensive business solutions including:</p>
            <ul>
                <li>Advanced Point of Sale systems</li>
                <li>Customer relationship management</li>
                <li>Business analytics and reporting</li>
                <li>Multi-location management</li>
                <li>Secure, PCI-compliant operations</li>
            </ul>
            
            <p>To complete your registration, please click the button below:</p>
            
            <div style="text-align: center;">
                <a href="${signupUrl}" class="button">Complete Registration</a>
            </div>
            
            <div class="warning">
                <strong>Important:</strong> This invitation link will expire on <strong>${expirationTime}</strong>. 
                Please complete your registration before this time.
            </div>
            
            <p>If you're unable to click the button above, you can copy and paste this link into your browser:</p>
            <p style="word-break: break-all; background: #e9ecef; padding: 10px; border-radius: 3px; font-family: monospace;">
                ${signupUrl}
            </p>
            
            <p>If you have any questions or need assistance, please don't hesitate to contact our support team:</p>
            <ul>
                <li>Email: <EMAIL></li>
                <li>Phone: 1-800-CONNECT</li>
                <li>Hours: Monday-Friday, 9AM-6PM EST</li>
            </ul>
        </div>
        
        <div class="footer">
            <p>© ${new Date().getFullYear()} Connect+ Apps. All rights reserved.</p>
            <p>This is an automated message. Please do not reply to this email.</p>
        </div>
    </div>
</body>
</html>
  `.trim();

  return { subject, body };
}

/**
 * Validates email addresses for bulk invitations
 * @param {string[]} emails - Array of email addresses
 * @returns {object} Validation results
 */
export function validateEmailList(emails) {
  const valid = [];
  const invalid = [];
  const duplicates = [];
  const seen = new Set();

  emails.forEach(email => {
    const trimmedEmail = email.trim().toLowerCase();
    
    if (!trimmedEmail) {
      return; // Skip empty emails
    }
    
    if (seen.has(trimmedEmail)) {
      duplicates.push(email);
      return;
    }
    
    seen.add(trimmedEmail);
    
    if (isValidEmail(trimmedEmail)) {
      valid.push(trimmedEmail);
    } else {
      invalid.push(email);
    }
  });

  return { valid, invalid, duplicates };
}

/**
 * Simple email validation
 * @param {string} email - Email to validate
 * @returns {boolean} True if email is valid
 */
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Sends bulk signup invitations
 * @param {string[]} emails - Array of email addresses
 * @param {string} senderName - Name of sender
 * @param {string} customMessage - Custom message
 * @returns {Promise<object>} Results of bulk send operation
 */
export async function sendBulkInvitations(emails, senderName = 'Connect+ Apps Team', customMessage = '') {
  const validation = validateEmailList(emails);
  const results = {
    successful: [],
    failed: [],
    invalid: validation.invalid,
    duplicates: validation.duplicates,
    total: validation.valid.length
  };

  // Send invitations to valid emails
  for (const email of validation.valid) {
    try {
      const result = await sendSignupInvitation(email, senderName, customMessage);
      
      if (result.success) {
        results.successful.push({ email, token: result.token });
      } else {
        results.failed.push({ email, error: result.error });
      }
      
      // Add small delay to avoid overwhelming the email service
      await new Promise(resolve => setTimeout(resolve, 100));
      
    } catch (error) {
      results.failed.push({ email, error: 'Unexpected error occurred' });
    }
  }

  return results;
}
