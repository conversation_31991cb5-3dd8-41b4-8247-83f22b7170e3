import { supabase } from '../supabaseClient';

/**
 * Validates a signup token
 * @param {string} token - The token to validate
 * @returns {Promise<{isValid: boolean, tokenData: object|null, error: string|null}>}
 */
export async function validateToken(token) {
  try {
    if (!token || typeof token !== 'string') {
      return {
        isValid: false,
        tokenData: null,
        error: 'Invalid token format'
      };
    }

    if (!supabase) {
      return {
        isValid: false,
        tokenData: null,
        error: 'Service temporarily unavailable'
      };
    }

    // Call the database function to validate the token
    const { data, error } = await supabase.rpc('validate_signup_token', {
      token_value: token
    });

    if (error) {
      console.error('Token validation error:', error);
      return {
        isValid: false,
        tokenData: null,
        error: 'Token validation failed'
      };
    }

    if (!data || data.length === 0) {
      return {
        isValid: false,
        tokenData: null,
        error: 'Invalid token'
      };
    }

    const result = data[0];
    
    return {
      isValid: result.is_valid,
      tokenData: result.is_valid ? {
        tokenId: result.token_id,
        email: result.email,
        expiresAt: result.expires_at
      } : null,
      error: result.error_message
    };

  } catch (err) {
    console.error('Token validation exception:', err);
    return {
      isValid: false,
      tokenData: null,
      error: 'Service error occurred'
    };
  }
}

/**
 * Marks a token as used after successful registration
 * @param {string} token - The token to mark as used
 * @returns {Promise<{success: boolean, error: string|null}>}
 */
export async function markTokenAsUsed(token) {
  try {
    if (!supabase) {
      return {
        success: false,
        error: 'Service temporarily unavailable'
      };
    }

    const { data, error } = await supabase.rpc('mark_token_used', {
      token_value: token
    });

    if (error) {
      console.error('Mark token used error:', error);
      return {
        success: false,
        error: 'Failed to mark token as used'
      };
    }

    return {
      success: data === true,
      error: data === true ? null : 'Token could not be marked as used'
    };

  } catch (err) {
    console.error('Mark token used exception:', err);
    return {
      success: false,
      error: 'Service error occurred'
    };
  }
}

/**
 * Submits a customer registration
 * @param {object} registrationData - The registration form data
 * @param {string} token - The signup token
 * @param {string} ipAddress - User's IP address
 * @param {string} userAgent - User's browser user agent
 * @returns {Promise<{success: boolean, registrationId: string|null, error: string|null}>}
 */
export async function submitRegistration(registrationData, token, ipAddress = null, userAgent = null) {
  try {
    if (!supabase) {
      return {
        success: false,
        registrationId: null,
        error: 'Service temporarily unavailable'
      };
    }

    // First validate the token to get token_id
    const tokenValidation = await validateToken(token);
    if (!tokenValidation.isValid) {
      return {
        success: false,
        registrationId: null,
        error: tokenValidation.error || 'Invalid token'
      };
    }

    // Insert the registration
    const { data: registrationResult, error: registrationError } = await supabase
      .from('customer_registrations')
      .insert({
        token_id: tokenValidation.tokenData.tokenId,
        email: registrationData.email,
        first_name: registrationData.firstName,
        last_name: registrationData.lastName,
        company_name: registrationData.companyName,
        phone: registrationData.phone,
        business_type: registrationData.businessType,
        business_size: registrationData.businessSize,
        current_pos_system: registrationData.currentPosSystem,
        specific_needs: registrationData.specificNeeds,
        preferred_contact_method: registrationData.preferredContactMethod,
        marketing_consent: registrationData.marketingConsent,
        registration_ip: ipAddress,
        user_agent: userAgent
      })
      .select('id')
      .single();

    if (registrationError) {
      console.error('Registration insert error:', registrationError);
      return {
        success: false,
        registrationId: null,
        error: 'Registration failed'
      };
    }

    // Record terms acceptance
    const { error: termsError } = await supabase
      .from('terms_acceptances')
      .insert({
        registration_id: registrationResult.id,
        terms_version: '1.0',
        ip_address: ipAddress,
        user_agent: userAgent,
        acceptance_method: 'checkbox'
      });

    if (termsError) {
      console.error('Terms acceptance error:', termsError);
      // Don't fail the registration for terms logging error
    }

    // Mark token as used
    await markTokenAsUsed(token);

    return {
      success: true,
      registrationId: registrationResult.id,
      error: null
    };

  } catch (err) {
    console.error('Registration submission exception:', err);
    return {
      success: false,
      registrationId: null,
      error: 'Registration failed due to system error'
    };
  }
}
