import { useState, useEffect } from 'react';
import { validateToken } from '../services/authService';
import { isValidTokenFormat } from '../utils/tokenUtils';

/**
 * Custom hook for token validation
 * @param {string} token - Token to validate
 * @returns {object} - Validation state and data
 */
export function useTokenValidation(token) {
  const [state, setState] = useState({
    isLoading: true,
    isValid: false,
    tokenData: null,
    error: null
  });

  useEffect(() => {
    let isMounted = true;

    async function validateTokenAsync() {
      // Reset state
      setState({
        isLoading: true,
        isValid: false,
        tokenData: null,
        error: null
      });

      // Basic format validation first
      if (!isValidTokenFormat(token)) {
        if (isMounted) {
          setState({
            isLoading: false,
            isValid: false,
            tokenData: null,
            error: 'Invalid token format'
          });
        }
        return;
      }

      try {
        const result = await validateToken(token);
        
        if (isMounted) {
          setState({
            isLoading: false,
            isValid: result.isValid,
            tokenData: result.tokenData,
            error: result.error
          });
        }
      } catch (error) {
        console.error('Token validation error:', error);
        if (isMounted) {
          setState({
            isLoading: false,
            isValid: false,
            tokenData: null,
            error: 'Failed to validate token'
          });
        }
      }
    }

    if (token) {
      validateTokenAsync();
    } else {
      setState({
        isLoading: false,
        isValid: false,
        tokenData: null,
        error: 'No token provided'
      });
    }

    return () => {
      isMounted = false;
    };
  }, [token]);

  return state;
}
